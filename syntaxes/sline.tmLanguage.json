{"scopeName": "source.sline", "name": "Sline", "patterns": [{"include": "#comments"}, {"include": "#raw_values"}, {"include": "#block_helpers"}, {"include": "#end_blocks"}, {"include": "#else_blocks"}, {"include": "#self_closing_tags"}, {"include": "#variables_and_partials"}, {"include": "text.html.basic"}], "repository": {"comments": {"patterns": [{"name": "comment.block.sline", "begin": "{{!--", "end": "--}}", "beginCaptures": {"0": {"name": "punctuation.definition.comment.begin.sline"}}, "endCaptures": {"0": {"name": "punctuation.definition.comment.end.sline"}}}, {"name": "comment.block.sline", "begin": "{{!", "end": "}}", "beginCaptures": {"0": {"name": "punctuation.definition.comment.begin.sline"}}, "endCaptures": {"0": {"name": "punctuation.definition.comment.end.sline"}}}]}, "block_helpers": {"patterns": [{"name": "meta.function.block.start.sline", "begin": "(\\{\\{)(~?)(#)([a-zA-Z_][a-zA-Z0-9_\\-/]*)", "end": "(~?)(\\}\\})", "beginCaptures": {"1": {"name": "punctuation.definition.tag.sline"}, "2": {"name": "punctuation.definition.whitespace-control.sline"}, "3": {"name": "punctuation.definition.tag.sline"}, "4": {"name": "entity.name.function.sline"}}, "endCaptures": {"1": {"name": "punctuation.definition.whitespace-control.sline"}, "2": {"name": "punctuation.definition.tag.sline"}}, "patterns": [{"include": "#sline_expressions"}]}]}, "end_blocks": {"patterns": [{"name": "meta.function.block.end.sline", "match": "(\\{\\{)(~?/)([a-zA-Z_][a-zA-Z0-9_\\-/]*)(~?\\}\\})", "captures": {"1": {"name": "punctuation.definition.tag.sline"}, "2": {"name": "punctuation.definition.tag.sline"}, "3": {"name": "entity.name.function.sline"}, "4": {"name": "punctuation.definition.tag.sline"}}}]}, "else_blocks": {"patterns": [{"name": "meta.function.block.else.sline", "match": "(\\{\\{)(~?else)(~?\\}\\})", "captures": {"1": {"name": "punctuation.definition.tag.sline"}, "2": {"name": "keyword.control.flow.sline"}, "3": {"name": "punctuation.definition.tag.sline"}}}]}, "variables_and_partials": {"patterns": [{"name": "meta.function.inline.sline", "begin": "(\\{\\{)(~?#?)([a-zA-Z_][a-zA-Z0-9_\\-/]*)", "end": "(/~?\\}\\})", "beginCaptures": {"1": {"name": "punctuation.definition.tag.sline"}, "2": {"name": "punctuation.definition.tag.sline"}, "3": {"name": "entity.name.function.sline"}}, "endCaptures": {"1": {"name": "punctuation.definition.tag.sline"}}, "patterns": [{"include": "#sline_strings"}, {"include": "#attributes"}, {"include": "#filters"}, {"include": "#variables"}]}, {"name": "meta.expression.sline", "begin": "(\\{\\{~?)", "end": "(~?\\}\\})", "beginCaptures": {"1": {"name": "punctuation.definition.tag.sline"}}, "endCaptures": {"1": {"name": "punctuation.definition.tag.sline"}}, "patterns": [{"include": "#sline_strings"}, {"include": "#filters"}, {"include": "#variables"}]}]}, "sline_strings": {"patterns": [{"name": "string.quoted.double.sline", "begin": "\"(?=[^\"]*?(?:\\}\\}|\\|))", "end": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.sline"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.sline"}}, "patterns": [{"name": "constant.character.escape.sline", "match": "\\\\."}]}, {"name": "string.quoted.single.sline", "begin": "'(?=[^']*?(?:\\}\\}|\\|))", "end": "'", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.sline"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.sline"}}, "patterns": [{"name": "constant.character.escape.sline", "match": "\\\\."}]}]}, "strings": {"patterns": [{"name": "string.quoted.double.html", "begin": "\"", "end": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.html"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.html"}}, "patterns": [{"include": "#sline_expression_in_string"}, {"name": "constant.character.escape.html", "match": "\\\\."}]}, {"name": "string.quoted.single.html", "begin": "'", "end": "'", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.html"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.html"}}, "patterns": [{"include": "#sline_expression_in_string"}, {"name": "constant.character.escape.html", "match": "\\\\."}]}]}, "sline_expression_in_string": {"patterns": [{"name": "meta.embedded.sline", "begin": "\\{\\{", "end": "\\}\\}", "beginCaptures": {"0": {"name": "punctuation.definition.tag.sline"}}, "endCaptures": {"0": {"name": "punctuation.definition.tag.sline"}}, "patterns": [{"include": "#sline_strings"}, {"include": "#filters"}, {"include": "#variables"}]}]}, "attributes": {"patterns": [{"name": "entity.other.attribute-name.sline", "match": "\\b([a-zA-Z_][a-zA-Z0-9_\\-]*)\\s*=", "captures": {"1": {"name": "variable.parameter.sline"}}}]}, "raw_values": {"patterns": [{"name": "meta.raw.sline", "begin": "(\\{\\{\\{)(~?)", "end": "(~?)(\\}\\}\\})", "beginCaptures": {"1": {"name": "punctuation.definition.raw.begin.sline"}, "2": {"name": "punctuation.definition.whitespace-control.sline"}}, "endCaptures": {"1": {"name": "punctuation.definition.whitespace-control.sline"}, "2": {"name": "punctuation.definition.raw.end.sline"}}, "patterns": [{"include": "#sline_expressions"}]}]}, "self_closing_tags": {"patterns": [{"name": "meta.function.self-closing.sline", "begin": "(\\{\\{)(~?#)([a-zA-Z_][a-zA-Z0-9_\\-/]*)", "end": "(~?/)(~?\\}\\})", "beginCaptures": {"1": {"name": "punctuation.definition.tag.sline"}, "2": {"name": "punctuation.definition.tag.sline"}, "3": {"name": "entity.name.function.sline"}}, "endCaptures": {"1": {"name": "punctuation.definition.tag.sline"}, "2": {"name": "punctuation.definition.tag.sline"}}, "patterns": [{"include": "#sline_expressions"}]}]}, "filters": {"patterns": [{"name": "meta.filter.sline", "match": "(\\|)\\s*([a-zA-Z_][a-zA-Z0-9_]*)(\\()", "captures": {"1": {"name": "keyword.operator.pipe.sline"}, "2": {"name": "support.function.filter.sline"}, "3": {"name": "punctuation.section.parens.begin.sline"}}}, {"name": "punctuation.section.parens.end.sline", "match": "\\)"}]}, "sline_expressions": {"patterns": [{"include": "#sline_strings"}, {"include": "#sline_operators"}, {"include": "#sline_literals"}, {"include": "#filters"}, {"include": "#variables"}, {"include": "#attributes"}]}, "sline_operators": {"patterns": [{"name": "keyword.operator.logical.sline", "match": "(&&|\\|\\|)"}, {"name": "keyword.operator.comparison.sline", "match": "(==|!=|>=|<=|>|<)"}, {"name": "keyword.operator.unary.sline", "match": "!"}]}, "sline_literals": {"patterns": [{"name": "constant.language.nil.sline", "match": "\\bnil\\b"}, {"name": "constant.language.boolean.sline", "match": "\\b(true|false)\\b"}, {"name": "constant.numeric.float.sline", "match": "\\b\\d*\\.\\d+\\b"}, {"name": "constant.numeric.integer.sline", "match": "\\b\\d+\\b"}]}, "variables": {"patterns": [{"name": "variable.other.sline", "match": "\\b[a-zA-Z_][a-zA-Z0-9_]*(?:\\.[a-zA-Z_][a-zA-Z0-9_]*)*\\b"}, {"name": "meta.array-access.sline", "begin": "\\[", "end": "\\]", "beginCaptures": {"0": {"name": "punctuation.section.brackets.begin.sline"}}, "endCaptures": {"0": {"name": "punctuation.section.brackets.end.sline"}}, "patterns": [{"include": "#sline_expressions"}]}]}}}