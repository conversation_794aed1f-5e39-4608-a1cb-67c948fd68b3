{"scopeName": "source.sline", "name": "Sline", "patterns": [{"include": "#comments"}, {"include": "#block_helpers"}, {"include": "#end_blocks"}, {"include": "#else_blocks"}, {"include": "#variables_and_partials"}, {"include": "text.html.basic"}], "repository": {"comments": {"patterns": [{"name": "comment.block.sline", "begin": "{{!--", "end": "--}}", "beginCaptures": {"0": {"name": "punctuation.definition.comment.begin.sline"}}, "endCaptures": {"0": {"name": "punctuation.definition.comment.end.sline"}}}, {"name": "comment.block.sline", "begin": "{{!", "end": "}}", "beginCaptures": {"0": {"name": "punctuation.definition.comment.begin.sline"}}, "endCaptures": {"0": {"name": "punctuation.definition.comment.end.sline"}}}]}, "block_helpers": {"patterns": [{"name": "meta.function.block.start.sline", "begin": "(\\{\\{)(~?#)([a-zA-Z_][a-zA-Z0-9_\\-/]*)", "end": "(~?\\}\\})", "beginCaptures": {"1": {"name": "punctuation.definition.tag.sline"}, "2": {"name": "punctuation.definition.tag.sline"}, "3": {"name": "entity.name.function.sline"}}, "endCaptures": {"1": {"name": "punctuation.definition.tag.sline"}}, "patterns": [{"include": "#sline_strings"}, {"include": "#attributes"}, {"include": "#filters"}, {"include": "#variables"}]}]}, "end_blocks": {"patterns": [{"name": "meta.function.block.end.sline", "match": "(\\{\\{)(~?/)([a-zA-Z_][a-zA-Z0-9_\\-/]*)(~?\\}\\})", "captures": {"1": {"name": "punctuation.definition.tag.sline"}, "2": {"name": "punctuation.definition.tag.sline"}, "3": {"name": "entity.name.function.sline"}, "4": {"name": "punctuation.definition.tag.sline"}}}]}, "else_blocks": {"patterns": [{"name": "meta.function.block.else.sline", "match": "(\\{\\{)(~?else)(~?\\}\\})", "captures": {"1": {"name": "punctuation.definition.tag.sline"}, "2": {"name": "keyword.control.flow.sline"}, "3": {"name": "punctuation.definition.tag.sline"}}}]}, "variables_and_partials": {"patterns": [{"name": "meta.function.inline.sline", "begin": "(\\{\\{)(~?#?)([a-zA-Z_][a-zA-Z0-9_\\-/]*)", "end": "(/~?\\}\\})", "beginCaptures": {"1": {"name": "punctuation.definition.tag.sline"}, "2": {"name": "punctuation.definition.tag.sline"}, "3": {"name": "entity.name.function.sline"}}, "endCaptures": {"1": {"name": "punctuation.definition.tag.sline"}}, "patterns": [{"include": "#sline_strings"}, {"include": "#attributes"}, {"include": "#filters"}, {"include": "#variables"}]}, {"name": "meta.expression.sline", "begin": "(\\{\\{~?)", "end": "(~?\\}\\})", "beginCaptures": {"1": {"name": "punctuation.definition.tag.sline"}}, "endCaptures": {"1": {"name": "punctuation.definition.tag.sline"}}, "patterns": [{"include": "#sline_strings"}, {"include": "#filters"}, {"include": "#variables"}]}]}, "sline_strings": {"patterns": [{"name": "string.quoted.double.sline", "begin": "\"(?=[^\"]*?(?:\\}\\}|\\|))", "end": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.sline"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.sline"}}, "patterns": [{"name": "constant.character.escape.sline", "match": "\\\\."}]}, {"name": "string.quoted.single.sline", "begin": "'(?=[^']*?(?:\\}\\}|\\|))", "end": "'", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.sline"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.sline"}}, "patterns": [{"name": "constant.character.escape.sline", "match": "\\\\."}]}]}, "strings": {"patterns": [{"name": "string.quoted.double.html", "begin": "\"", "end": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.html"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.html"}}, "patterns": [{"include": "#sline_expression_in_string"}, {"name": "constant.character.escape.html", "match": "\\\\."}]}, {"name": "string.quoted.single.html", "begin": "'", "end": "'", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.html"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.html"}}, "patterns": [{"include": "#sline_expression_in_string"}, {"name": "constant.character.escape.html", "match": "\\\\."}]}]}, "sline_expression_in_string": {"patterns": [{"name": "meta.embedded.sline", "begin": "\\{\\{", "end": "\\}\\}", "beginCaptures": {"0": {"name": "punctuation.definition.tag.sline"}}, "endCaptures": {"0": {"name": "punctuation.definition.tag.sline"}}, "patterns": [{"include": "#sline_strings"}, {"include": "#filters"}, {"include": "#variables"}]}]}, "attributes": {"patterns": [{"name": "entity.other.attribute-name.sline", "match": "\\b([a-zA-Z_][a-zA-Z0-9_\\-]*)\\s*=", "captures": {"1": {"name": "variable.parameter.sline"}}}]}, "filters": {"patterns": [{"name": "meta.filter.sline", "match": "(\\|)\\s*([a-zA-Z_][a-zA-Z0-9_]*)(\\()", "captures": {"1": {"name": "keyword.operator.pipe.sline"}, "2": {"name": "support.function.filter.sline"}, "3": {"name": "punctuation.section.parens.begin.sline"}}}, {"name": "punctuation.section.parens.end.sline", "match": "\\)"}]}, "variables": {"patterns": [{"name": "variable.other.sline", "match": "\\b[a-zA-Z_][a-zA-Z0-9_\\.]*\\b"}]}}}