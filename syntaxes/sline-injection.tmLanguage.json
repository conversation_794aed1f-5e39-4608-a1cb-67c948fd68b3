{"scopeName": "sline.injection", "injectionSelector": "L:string.quoted.double.html, L:string.quoted.single.html", "patterns": [{"name": "meta.embedded.inline.sline", "begin": "\\{\\{", "end": "\\}\\}", "beginCaptures": {"0": {"name": "punctuation.definition.tag.sline"}}, "endCaptures": {"0": {"name": "punctuation.definition.tag.sline"}}, "patterns": [{"include": "#sline_content"}]}], "repository": {"sline_content": {"patterns": [{"include": "#block_helpers"}, {"include": "#end_blocks"}, {"include": "#else_blocks"}, {"include": "#variables_and_partials"}, {"include": "#sline_strings"}, {"include": "#filters"}, {"include": "#variables"}]}, "block_helpers": {"patterns": [{"name": "meta.function.block.start.sline", "match": "(\\{\\{)(~?#)([a-zA-Z_][a-zA-Z0-9_\\-/]*)(.*?)(~?\\}\\})", "captures": {"1": {"name": "punctuation.definition.tag.sline"}, "2": {"name": "punctuation.definition.tag.sline"}, "3": {"name": "entity.name.function.sline"}, "4": {"patterns": [{"include": "#sline_strings"}, {"include": "#attributes"}, {"include": "#filters"}, {"include": "#variables"}]}, "5": {"name": "punctuation.definition.tag.sline"}}}]}, "end_blocks": {"patterns": [{"name": "meta.function.block.end.sline", "match": "(\\{\\{)(~?/)([a-zA-Z_][a-zA-Z0-9_\\-/]*)(~?\\}\\})", "captures": {"1": {"name": "punctuation.definition.tag.sline"}, "2": {"name": "punctuation.definition.tag.sline"}, "3": {"name": "entity.name.function.sline"}, "4": {"name": "punctuation.definition.tag.sline"}}}]}, "else_blocks": {"patterns": [{"name": "meta.function.block.else.sline", "match": "(\\{\\{)(~?else)(~?\\}\\})", "captures": {"1": {"name": "punctuation.definition.tag.sline"}, "2": {"name": "keyword.control.flow.sline"}, "3": {"name": "punctuation.definition.tag.sline"}}}]}, "variables_and_partials": {"patterns": [{"name": "meta.function.inline.sline", "match": "(\\{\\{)(~?#?)([a-zA-Z_][a-zA-Z0-9_\\-/]*)(.*?)(/~?\\}\\})", "captures": {"1": {"name": "punctuation.definition.tag.sline"}, "2": {"name": "punctuation.definition.tag.sline"}, "3": {"name": "entity.name.function.sline"}, "4": {"patterns": [{"include": "#sline_strings"}, {"include": "#attributes"}, {"include": "#filters"}, {"include": "#variables"}]}, "5": {"name": "punctuation.definition.tag.sline"}}}, {"name": "meta.expression.sline", "match": "(\\{\\{~?)([^}]+)(~?\\}\\})", "captures": {"1": {"name": "punctuation.definition.tag.sline"}, "2": {"patterns": [{"include": "#sline_strings"}, {"include": "#filters"}, {"include": "#variables"}]}, "3": {"name": "punctuation.definition.tag.sline"}}}]}, "sline_strings": {"patterns": [{"name": "string.quoted.double.sline", "begin": "\"", "end": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.sline"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.sline"}}, "patterns": [{"name": "constant.character.escape.sline", "match": "\\\\."}]}, {"name": "string.quoted.single.sline", "begin": "'", "end": "'", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.sline"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.sline"}}, "patterns": [{"name": "constant.character.escape.sline", "match": "\\\\."}]}]}, "filters": {"patterns": [{"match": "(\\|)\\s*([a-zA-Z_][a-zA-Z0-9_]*)(\\()?", "captures": {"1": {"name": "punctuation.separator.pipe.sline"}, "2": {"name": "support.function.filter.sline"}, "3": {"name": "punctuation.section.parens.begin.sline"}}}, {"name": "punctuation.section.parens.end.sline", "match": "\\)"}]}, "variables": {"patterns": [{"name": "variable.other.sline", "match": "[a-zA-Z_][a-zA-Z0-9_]*(?:\\.[a-zA-Z_][a-zA-Z0-9_]*)*"}]}, "attributes": {"patterns": [{"name": "entity.other.attribute-name.sline", "match": "[a-zA-Z_][a-zA-Z0-9_]*(?=\\s*=)"}, {"name": "keyword.operator.assignment.sline", "match": "="}]}}}