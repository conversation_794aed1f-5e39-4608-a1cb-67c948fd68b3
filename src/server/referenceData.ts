// SLine 语言服务器参考数据管理器
import * as fs from 'fs';
import * as path from 'path';
import { CompletionItem, CompletionItemKind, MarkupKind } from 'vscode-languageserver/node';

// 接口定义
export interface SlineTagArgument {
    name: string;
    types: string[];
    description: string;
    description_cn: string;
}

export interface SlineTagHash {
    name: string;
    types: string[];
    description: string;
    description_cn?: string;
}

export interface SlineTagExample {
    name?: string;
    name_cn?: string;
    path?: string;
    raw_sline: string;
    syntax?: string;
    source_object?: string;
}

export interface SlineTag {
    name: string;
    syntax: string;
    summary: string;
    summary_cn: string;
    arguments?: SlineTagArgument[];
    examples: SlineTagExample[];
    hashs?: SlineTagHash[];
    link: string;
    deprecated: boolean;
    syntax_keywords?: Array<{
        keyword: string;
        description: string;
        description_cn: string;
    }>;
}

export interface SlineObjectProperty {
    name: string;
    return_type: string;
    array_return_type?: string;
    summary: string;
    summary_cn: string;
    deprecated?: boolean;
    description?: string;
    description_cn?: string;
}

export interface SlineObject {
    name: string;
    summary: string;
    summary_cn: string;
    properties: SlineObjectProperty[];
    link: string;
    deprecated?: boolean;
    description?: string;
    description_cn?: string;
}

export interface SlineFilterArgument {
    name: string;
    types: string[];
    description: string;
    description_cn: string;
}

export interface SlineFilterExample {
    name?: string;
    name_cn?: string;
    path?: string;
    raw_sline: string;
    syntax?: string;
    source_object?: string;
}

export interface SlineFilter {
    name: string;
    summary: string;
    summary_cn: string;
    arguments: SlineFilterArgument[];
    return_type: string | string[];
    examples: SlineFilterExample[];
    hashs?: SlineTagHash[];
    syntax: string;
    link: string;
    deprecated?: boolean;
    description?: string;
    description_cn?: string;
}

// 完成项上下文类型
export interface CompletionContext {
    type: 'tag' | 'filter' | 'object_property' | 'tag_parameter' | 'filter_parameter' | 'unknown';
    prefix: string;
    objectName?: string;
    tagName?: string;
    filterName?: string;
    parameterName?: string;
}

// 悬停信息接口
export interface HoverInfo {
    name: string;
    summary: string;
    summary_cn: string;
    syntax?: string;
    parameters?: Array<{
        name: string;
        types: string[];
        description: string;
    }>;
    examples?: SlineTagExample[] | SlineFilterExample[];
    link: string;
    deprecated?: boolean;
    returnType?: string;
}

/**
 * SLine 参考数据管理器
 * 负责加载、解析和索引 JSON 参考数据文件
 */
export class ReferenceDataManager {
    private tags: Map<string, SlineTag> = new Map();
    private objects: Map<string, SlineObject> = new Map();
    private filters: Map<string, SlineFilter> = new Map();
    private objectProperties: Map<string, SlineObjectProperty[]> = new Map();
    
    // 预计算的完成项缓存
    private tagCompletionItems: CompletionItem[] = [];
    private filterCompletionItems: CompletionItem[] = [];
    private objectCompletionItems: CompletionItem[] = [];

    constructor(private extensionPath: string) {}

    /**
     * 加载所有参考数据文件
     */
    async loadReferenceData(): Promise<void> {
        try {
            console.log('Loading SLine reference data...');

            // 加载 JSON 文件
            const tagsData = await this.loadJsonFile('tag.json');
            const objectsData = await this.loadJsonFile('objects.json');
            const filtersData = await this.loadJsonFile('filter.json');

            // 构建索引
            this.buildTagsIndex(tagsData);
            this.buildObjectsIndex(objectsData);
            this.buildFiltersIndex(filtersData);

            // 补充缺失的标签和过滤器（确保所有标签和过滤器都可用）
            this.supplementMissingTags();
            this.supplementMissingFilters();

            // 预计算完成项
            this.precomputeCompletionItems();

            console.log(`Loaded ${this.tags.size} tags, ${this.objects.size} objects, ${this.filters.size} filters`);

            // 如果没有加载到任何数据，使用回退数据
            if (this.tags.size === 0 && this.objects.size === 0 && this.filters.size === 0) {
                console.log('No reference data loaded, using fallback data...');
                this.loadFallbackData();
            }
        } catch (error) {
            console.error('Failed to load reference data:', error);
            // 如果加载失败，使用回退数据
            console.log('Using fallback data due to loading error...');
            this.loadFallbackData();
        }
    }

    /**
     * 加载 JSON 文件
     */
    private async loadJsonFile(filename: string): Promise<any[]> {
        // 尝试多个可能的路径
        const possiblePaths = [
            path.join(this.extensionPath, 'src', filename),
            path.join(this.extensionPath, filename),
            path.join(__dirname, '..', filename),
            path.join(__dirname, '..', 'src', filename),
            path.join(process.cwd(), 'src', filename)
        ];

        let filePath: string | null = null;
        for (const testPath of possiblePaths) {
            if (fs.existsSync(testPath)) {
                filePath = testPath;
                break;
            }
        }

        if (!filePath) {
            console.warn(`Reference file not found: ${filename}. Tried paths:`, possiblePaths);
            return [];
        }

        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const data = JSON.parse(content);
            console.log(`Loaded ${filename} from ${filePath}: ${Array.isArray(data) ? data.length : 'unknown'} items`);
            return Array.isArray(data) ? data : [];
        } catch (error) {
            console.error(`Error loading ${filename}:`, error);
            return [];
        }
    }

    /**
     * 构建标签索引
     */
    private buildTagsIndex(tagsData: SlineTag[]): void {
        for (const tag of tagsData) {
            this.tags.set(tag.name, tag);
        }
    }

    /**
     * 构建对象索引
     */
    private buildObjectsIndex(objectsData: SlineObject[]): void {
        for (const obj of objectsData) {
            this.objects.set(obj.name, obj);
            this.objectProperties.set(obj.name, obj.properties);
        }
    }

    /**
     * 构建过滤器索引
     */
    private buildFiltersIndex(filtersData: SlineFilter[]): void {
        for (const filter of filtersData) {
            this.filters.set(filter.name, filter);
        }
    }

    /**
     * 预计算完成项以提高性能
     */
    private precomputeCompletionItems(): void {
        // 预计算标签完成项
        this.tagCompletionItems = Array.from(this.tags.values()).map(tag => 
            this.createTagCompletionItem(tag)
        );

        // 预计算过滤器完成项
        this.filterCompletionItems = Array.from(this.filters.values()).map(filter => 
            this.createFilterCompletionItem(filter)
        );

        // 预计算对象完成项
        this.objectCompletionItems = Array.from(this.objects.values()).map(obj => 
            this.createObjectCompletionItem(obj)
        );
    }

    /**
     * 创建标签完成项
     */
    private createTagCompletionItem(tag: SlineTag): CompletionItem {
        const hasArguments = tag.arguments && tag.arguments.length > 0;
        const hasHashs = tag.hashs && tag.hashs.length > 0;

        // 检查是否是自闭合标签
        const isSelfClosingTag = this.isSelfClosingTag(tag.name);

        let insertText = tag.name;

        if (isSelfClosingTag) {
            // 自闭合标签：只插入标签名，让用户手动添加参数和 /
            if (hasArguments || hasHashs) {
                insertText += ' '; // 添加空格，方便用户输入参数
            }
        } else {
            // 需要闭合的标签：只插入标签名，避免复杂的自动完成
            if (hasArguments || hasHashs) {
                insertText += ' '; // 添加空格，方便用户输入参数
            }
        }

        return {
            label: tag.name,
            kind: CompletionItemKind.Function,
            detail: `Sline Tag - ${tag.summary}${isSelfClosingTag ? ' (自闭合)' : ''}`,
            documentation: {
                kind: MarkupKind.Markdown,
                value: this.formatTagDocumentation(tag)
            },
            insertText: insertText,
            data: { type: 'tag', name: tag.name },
            deprecated: tag.deprecated,
            sortText: tag.deprecated ? 'z' + tag.name : 'a' + tag.name
        };
    }

    /**
     * 判断标签是否是自闭合标签
     */
    private isSelfClosingTag(tagName: string): boolean {
        const selfClosingTags = [
            'layout', 'var', 'component', 'content', 'section', 'sections',
            'stylesheet', 'script', 'meta-tags', 'set', 'image_tag', 'external_video_tag',
            'video_tag', 'time_tag', 'metafield_tag', 'preload_tag', 'payment_button'
        ];
        return selfClosingTags.includes(tagName);
    }

    /**
     * 创建过滤器完成项
     */
    private createFilterCompletionItem(filter: SlineFilter): CompletionItem {
        const hasArguments = filter.arguments && filter.arguments.length > 0;
        
        let insertText = filter.name;
        if (hasArguments) {
            insertText += '($0)'; // 带参数的过滤器
        } else {
            insertText += '()';
        }

        return {
            label: filter.name,
            kind: CompletionItemKind.Function,
            detail: `Sline Filter - ${filter.summary}`,
            documentation: {
                kind: MarkupKind.Markdown,
                value: this.formatFilterDocumentation(filter)
            },
            insertText: insertText,
            data: { type: 'filter', name: filter.name },
            deprecated: filter.deprecated,
            sortText: filter.deprecated ? 'z' + filter.name : 'a' + filter.name
        };
    }

    /**
     * 创建对象完成项
     */
    private createObjectCompletionItem(obj: SlineObject): CompletionItem {
        return {
            label: obj.name,
            kind: CompletionItemKind.Class,
            detail: `Sline Object - ${obj.summary}`,
            documentation: {
                kind: MarkupKind.Markdown,
                value: this.formatObjectDocumentation(obj)
            },
            insertText: obj.name,
            data: { type: 'object', name: obj.name },
            deprecated: obj.deprecated,
            sortText: obj.deprecated ? 'z' + obj.name : 'a' + obj.name
        };
    }

    /**
     * 格式化标签文档
     */
    private formatTagDocumentation(tag: SlineTag): string {
        let doc = `# ${tag.name}\n\n${tag.summary}\n\n`;
        
        if (tag.syntax) {
            doc += `## Syntax\n\`\`\`sline\n${tag.syntax}\n\`\`\`\n\n`;
        }

        if (tag.arguments && tag.arguments.length > 0) {
            doc += `## Arguments\n`;
            for (const arg of tag.arguments) {
                doc += `- **${arg.name}** (${arg.types.join(' | ')}): ${arg.description}\n`;
            }
            doc += '\n';
        }

        if (tag.hashs && tag.hashs.length > 0) {
            doc += `## Hash Parameters\n`;
            for (const hash of tag.hashs) {
                doc += `- **${hash.name}** (${hash.types.join(' | ')}): ${hash.description}\n`;
            }
            doc += '\n';
        }

        if (tag.examples && tag.examples.length > 0) {
            doc += `## Example\n\`\`\`sline\n${tag.examples[0].raw_sline}\n\`\`\`\n\n`;
        }

        doc += `[📖 Documentation](${tag.link})`;
        
        return doc;
    }

    /**
     * 格式化过滤器文档
     */
    private formatFilterDocumentation(filter: SlineFilter): string {
        let doc = `# ${filter.name}\n\n${filter.summary}\n\n`;
        
        if (filter.syntax) {
            doc += `## Syntax\n\`\`\`sline\n${filter.syntax}\n\`\`\`\n\n`;
        }

        if (filter.arguments && filter.arguments.length > 0) {
            doc += `## Arguments\n`;
            for (const arg of filter.arguments) {
                doc += `- **${arg.name}** (${arg.types.join(' | ')}): ${arg.description}\n`;
            }
            doc += '\n';
        }

        const returnType = Array.isArray(filter.return_type) ? filter.return_type.join(' | ') : filter.return_type;
        if (returnType) {
            doc += `## Returns\n**${returnType}**\n\n`;
        }

        if (filter.examples && filter.examples.length > 0) {
            doc += `## Example\n\`\`\`sline\n${filter.examples[0].raw_sline}\n\`\`\`\n\n`;
        }

        doc += `[📖 Documentation](${filter.link})`;
        
        return doc;
    }

    /**
     * 格式化对象文档
     */
    private formatObjectDocumentation(obj: SlineObject): string {
        let doc = `# ${obj.name}\n\n${obj.summary}\n\n`;
        
        if (obj.properties && obj.properties.length > 0) {
            doc += `## Properties\n`;
            for (const prop of obj.properties.slice(0, 10)) { // 限制显示前10个属性
                doc += `- **${prop.name}** (${prop.return_type}): ${prop.summary}\n`;
            }
            if (obj.properties.length > 10) {
                doc += `- ... and ${obj.properties.length - 10} more properties\n`;
            }
            doc += '\n';
        }

        doc += `[📖 Documentation](${obj.link})`;
        
        return doc;
    }

    /**
     * 获取标签完成建议
     */
    getTagCompletions(prefix: string): CompletionItem[] {
        if (!prefix) {
            return this.tagCompletionItems;
        }

        return this.tagCompletionItems.filter(item =>
            item.label.toLowerCase().startsWith(prefix.toLowerCase())
        );
    }

    /**
     * 获取过滤器完成建议
     */
    getFilterCompletions(prefix: string): CompletionItem[] {
        if (!prefix) {
            return this.filterCompletionItems;
        }

        return this.filterCompletionItems.filter(item =>
            item.label.toLowerCase().startsWith(prefix.toLowerCase())
        );
    }

    /**
     * 获取对象属性完成建议
     */
    getObjectPropertyCompletions(objectName: string, prefix: string): CompletionItem[] {
        const properties = this.objectProperties.get(objectName);
        if (!properties) {
            return [];
        }

        return properties
            .filter(prop => prop.name.toLowerCase().startsWith(prefix.toLowerCase()))
            .map(prop => this.createPropertyCompletionItem(prop));
    }

    /**
     * 创建属性完成项
     */
    private createPropertyCompletionItem(property: SlineObjectProperty): CompletionItem {
        return {
            label: property.name,
            kind: CompletionItemKind.Property,
            detail: `${property.return_type} - ${property.summary}`,
            documentation: {
                kind: MarkupKind.Markdown,
                value: `**${property.name}** (${property.return_type})\n\n${property.summary}\n\n${property.description || ''}`
            },
            insertText: property.name,
            data: { type: 'property', name: property.name },
            deprecated: property.deprecated,
            sortText: property.deprecated ? 'z' + property.name : 'a' + property.name
        };
    }

    /**
     * 获取标签参数完成建议
     */
    getTagParameterCompletions(tagName: string, prefix: string): CompletionItem[] {
        const tag = this.tags.get(tagName);
        if (!tag) {
            return [];
        }

        const completions: CompletionItem[] = [];

        // 添加参数建议
        if (tag.arguments) {
            for (const arg of tag.arguments) {
                if (arg.name.toLowerCase().startsWith(prefix.toLowerCase())) {
                    completions.push({
                        label: arg.name,
                        kind: CompletionItemKind.Variable,
                        detail: `Parameter - ${arg.types.join(' | ')}`,
                        documentation: arg.description,
                        insertText: arg.name,
                        data: { type: 'parameter', name: arg.name }
                    });
                }
            }
        }

        // 添加哈希参数建议
        if (tag.hashs) {
            for (const hash of tag.hashs) {
                if (hash.name.toLowerCase().startsWith(prefix.toLowerCase())) {
                    completions.push({
                        label: hash.name,
                        kind: CompletionItemKind.Variable,
                        detail: `Hash Parameter - ${hash.types.join(' | ')}`,
                        documentation: hash.description,
                        insertText: `${hash.name}=`,
                        data: { type: 'hash_parameter', name: hash.name }
                    });
                }
            }
        }

        return completions;
    }

    /**
     * 获取过滤器参数完成建议
     */
    getFilterParameterCompletions(filterName: string, prefix: string): CompletionItem[] {
        const filter = this.filters.get(filterName);
        if (!filter || !filter.arguments) {
            return [];
        }

        return filter.arguments
            .filter(arg => arg.name.toLowerCase().startsWith(prefix.toLowerCase()))
            .map(arg => ({
                label: arg.name,
                kind: CompletionItemKind.Variable,
                detail: `Parameter - ${arg.types.join(' | ')}`,
                documentation: arg.description,
                insertText: arg.name,
                data: { type: 'filter_parameter', name: arg.name }
            }));
    }

    /**
     * 获取悬停信息
     */
    getHoverInfo(word: string, context?: CompletionContext): HoverInfo | null {
        // 检查是否是标签
        const tag = this.tags.get(word);
        if (tag) {
            return {
                name: tag.name,
                summary: tag.summary,
                summary_cn: tag.summary_cn,
                syntax: tag.syntax,
                parameters: tag.arguments?.map(arg => ({
                    name: arg.name,
                    types: arg.types,
                    description: arg.description
                })),
                examples: tag.examples,
                link: tag.link,
                deprecated: tag.deprecated
            };
        }

        // 检查是否是过滤器
        const filter = this.filters.get(word);
        if (filter) {
            return {
                name: filter.name,
                summary: filter.summary,
                summary_cn: filter.summary_cn,
                syntax: filter.syntax,
                parameters: filter.arguments?.map(arg => ({
                    name: arg.name,
                    types: arg.types,
                    description: arg.description
                })),
                examples: filter.examples,
                link: filter.link,
                deprecated: filter.deprecated,
                returnType: Array.isArray(filter.return_type) ? filter.return_type.join(' | ') : filter.return_type
            };
        }

        // 检查是否是对象
        const obj = this.objects.get(word);
        if (obj) {
            return {
                name: obj.name,
                summary: obj.summary,
                summary_cn: obj.summary_cn,
                link: obj.link,
                deprecated: obj.deprecated
            };
        }

        // 检查是否是对象属性
        if (context?.objectName) {
            const properties = this.objectProperties.get(context.objectName);
            const property = properties?.find(p => p.name === word);
            if (property) {
                return {
                    name: property.name,
                    summary: property.summary,
                    summary_cn: property.summary_cn,
                    link: '', // 属性通常没有独立的链接
                    deprecated: property.deprecated,
                    returnType: property.return_type
                };
            }
        }

        return null;
    }

    /**
     * 检查标签是否存在
     */
    hasTag(tagName: string): boolean {
        return this.tags.has(tagName);
    }

    /**
     * 检查过滤器是否存在
     */
    hasFilter(filterName: string): boolean {
        return this.filters.has(filterName);
    }

    /**
     * 检查对象是否存在
     */
    hasObject(objectName: string): boolean {
        return this.objects.has(objectName);
    }

    /**
     * 获取相似的标签建议（用于拼写错误检查）
     */
    getSimilarTags(tagName: string): string[] {
        const suggestions: string[] = [];
        const lowerTagName = tagName.toLowerCase();

        for (const [name] of this.tags) {
            const lowerName = name.toLowerCase();

            // 简单的相似度检查
            if (lowerName.includes(lowerTagName) || lowerTagName.includes(lowerName)) {
                suggestions.push(name);
            }
        }

        return suggestions.slice(0, 3); // 最多返回3个建议
    }

    /**
     * 获取相似的过滤器建议（用于拼写错误检查）
     */
    getSimilarFilters(filterName: string): string[] {
        const suggestions: string[] = [];
        const lowerFilterName = filterName.toLowerCase();

        for (const [name] of this.filters) {
            const lowerName = name.toLowerCase();

            // 简单的相似度检查
            if (lowerName.includes(lowerFilterName) || lowerFilterName.includes(lowerName)) {
                suggestions.push(name);
            }
        }

        return suggestions.slice(0, 3); // 最多返回3个建议
    }

    /**
     * 获取所有标签名称（用于向后兼容）
     */
    getAllTagNames(): string[] {
        return Array.from(this.tags.keys());
    }

    /**
     * 获取所有过滤器名称（用于向后兼容）
     */
    getAllFilterNames(): string[] {
        return Array.from(this.filters.keys());
    }

    /**
     * 获取所有对象名称
     */
    getAllObjectNames(): string[] {
        return Array.from(this.objects.keys());
    }

    /**
     * 补充缺失的标签 - 确保所有常用标签都可用
     */
    private supplementMissingTags(): void {
        // 基于 docs/sline_tags_list.md 的完整标签列表
        const allTags = [
            'activate_customer_password_form', 'bind_customer_email_form', 'bind_customer_phone_form',
            'block', 'blocks', 'cancel_delete_customer_form', 'capture', 'cart_form', 'case',
            'company_account_application_form', 'component', 'contact_form', 'content', 'create_customer_form',
            'customer_address_form', 'customer_form', 'customer_login_form', 'customer_login_link',
            'customer_logout_link', 'customer_register_link', 'customer_subscribe_form', 'customer_unsubscribe_form',
            'delete_customer_form', 'external_video_tag', 'for', 'format_address', 'highlight', 'if',
            'image_tag', 'layout', 'link_to', 'link_to_customer_login', 'link_to_customer_logout',
            'link_to_customer_register', 'localization_form', 'metafield_tag', 'new_comment_form',
            'order_tracking_form', 'payment_button', 'payment_type_svg', 'placeholder_svg', 'preload_tag',
            'reset_customer_password_form', 'schema', 'script', 'section', 'sections', 'set',
            'storefront_password_form', 'style', 'stylesheet', 'switch', 'time_tag', 'update_customer_form',
            'var', 'video_tag',
            // 额外的常用标签
            'comment', 'customer_register_form', 'default', 'each', 'else', 'elseif', 'form', 'liquid',
            'paginate', 'raw', 'recover_customer_password_form', 'tablerow', 'unless', 'with'
        ];

        for (const tagName of allTags) {
            if (!this.tags.has(tagName)) {
                const tag: SlineTag = {
                    name: tagName,
                    syntax: `{{#${tagName}}}...{{/${tagName}}}`,
                    summary: `SLine ${tagName} tag`,
                    summary_cn: `SLine ${tagName} 标签`,
                    examples: [],
                    link: '',
                    deprecated: false
                };
                this.tags.set(tagName, tag);
            }
        }
    }

    /**
     * 补充缺失的过滤器 - 确保所有常用过滤器都可用
     */
    private supplementMissingFilters(): void {
        const allFilters = [
            'abs', 'append', 'asset_url', 'at_least', 'at_most', 'camelize', 'capitalize', 'ceil',
            'class_list', 'concat', 'contains', 'css_var', 'date', 'default', 'divided_by', 'downcase',
            'ends_with', 'escape', 'external_video_url', 'file_img_url', 'file_url', 'first', 'floor',
            'font_face', 'font_modify', 'font_url', 'get', 'get_article_pagination', 'get_collections',
            'get_comment_pagination', 'get_metafields', 'get_order_pagination', 'get_pagination',
            'get_product', 'get_product_pagination', 'get_search_pagination', 'get_variants', 'handleize',
            'image_url', 'join', 'json', 'last', 'map', 'metafield_text', 'minus', 'modulo', 'money',
            'money_with_currency', 'money_without_currency', 'newline_to_br', 'payment_type_img_url',
            'pluralize', 'plus', 'prepend', 'remove', 'remove_first', 'replace', 'replace_first',
            'reverse', 'round', 'size', 'slice', 'sort', 'split', 'starts_with', 'strip_html',
            'strip_newlines', 't', 'times', 'trim', 'trim_left', 'trim_right', 'truncate',
            'truncate_words', 'uniq', 'upcase', 'url_decode', 'url_encode', 'url_escape',
            'url_param_escape', 'where'
        ];

        for (const filterName of allFilters) {
            if (!this.filters.has(filterName)) {
                const filter: SlineFilter = {
                    name: filterName,
                    syntax: `{{ value | ${filterName} }}`,
                    summary: `SLine ${filterName} filter`,
                    summary_cn: `SLine ${filterName} 过滤器`,
                    arguments: [],
                    examples: [],
                    link: '',
                    deprecated: false,
                    return_type: 'string'
                };
                this.filters.set(filterName, filter);
            }
        }
    }

    /**
     * 加载回退数据 - 当 JSON 文件无法加载时使用
     */
    private loadFallbackData(): void {
        console.log('Loading fallback SLine reference data...');

        // 基本标签数据
        const basicTags = [
            'if', 'else', 'each', 'for', 'with', 'unless',
            'component', 'layout', 'content', 'section', 'sections',
            'blocks', 'block', 'var', 'set', 'capture', 'schema', 'image_tag'
        ];

        // 基本过滤器数据
        const basicFilters = [
            'money', 'money_with_currency', 'money_without_currency',
            'date', 'format', 'upper', 'lower', 'capitalize', 'truncate',
            'trim', 'replace', 'split', 'first', 'last', 'size', 'join',
            'asset_url', 'append', 'prepend', 'strip_html', 'escape',
            'default', 't', 'json', 'class_list', 'get_variants', 'contains',
            'plus', 'minus', 'times', 'divided_by', 'modulo', 'abs', 'ceil', 'floor', 'round'
        ];

        // 基本对象数据
        const basicObjects = [
            'product', 'products', 'collection', 'collections', 'variant',
            'cart', 'order', 'customer', 'shop', 'request', 'settings',
            'routes', 'localization', 'article', 'articles', 'blog', 'blogs',
            'page', 'pages', 'block', 'section', 'props', 'forblock', 'this', 'forloop'
        ];

        // 创建基本标签对象
        for (const tagName of basicTags) {
            const tag: SlineTag = {
                name: tagName,
                syntax: `{{#${tagName}}}...{{/${tagName}}}`,
                summary: `SLine ${tagName} tag`,
                summary_cn: `SLine ${tagName} 标签`,
                examples: [],
                link: '',
                deprecated: false
            };
            this.tags.set(tagName, tag);
        }

        // 创建基本过滤器对象
        for (const filterName of basicFilters) {
            const filter: SlineFilter = {
                name: filterName,
                syntax: `{{ value | ${filterName} }}`,
                summary: `SLine ${filterName} filter`,
                summary_cn: `SLine ${filterName} 过滤器`,
                arguments: [],
                examples: [],
                link: '',
                deprecated: false,
                return_type: 'string'
            };
            this.filters.set(filterName, filter);
        }

        // 创建基本对象
        for (const objectName of basicObjects) {
            const obj: SlineObject = {
                name: objectName,
                summary: `SLine ${objectName} object`,
                summary_cn: `SLine ${objectName} 对象`,
                link: '',
                deprecated: false,
                properties: []
            };
            this.objects.set(objectName, obj);
        }

        // 预计算完成项
        this.precomputeCompletionItems();

        console.log(`Loaded fallback data: ${this.tags.size} tags, ${this.objects.size} objects, ${this.filters.size} filters`);
    }
}
