import * as fs from 'fs';
import * as path from 'path';

describe('注入语法测试', () => {
    let injectionGrammarContent: string;
    let packageJsonContent: string;
    
    beforeAll(() => {
        // 读取注入语法文件
        const injectionGrammarPath = path.join(__dirname, '../../syntaxes/sline-injection.tmLanguage.json');
        injectionGrammarContent = fs.readFileSync(injectionGrammarPath, 'utf8');
        
        // 读取 package.json
        const packageJsonPath = path.join(__dirname, '../../package.json');
        packageJsonContent = fs.readFileSync(packageJsonPath, 'utf8');
    });

    test('注入语法文件应该是有效的 JSON', () => {
        expect(() => {
            JSON.parse(injectionGrammarContent);
        }).not.toThrow();
    });

    test('注入语法应该有正确的结构', () => {
        const grammar = JSON.parse(injectionGrammarContent);
        
        // 检查必要的字段
        expect(grammar.scopeName).toBe('sline.injection');
        expect(grammar.injectionSelector).toBeDefined();
        expect(grammar.patterns).toBeDefined();
        expect(grammar.repository).toBeDefined();
        
        // 检查注入选择器是否针对 HTML 字符串
        expect(grammar.injectionSelector).toContain('string.quoted.double.html');
        expect(grammar.injectionSelector).toContain('string.quoted.single.html');
    });

    test('package.json 应该包含注入语法配置', () => {
        const packageJson = JSON.parse(packageJsonContent);
        
        // 检查是否有注入语法配置
        const injectionGrammar = packageJson.contributes.grammars.find(
            (g: any) => g.scopeName === 'sline.injection'
        );
        
        expect(injectionGrammar).toBeDefined();
        expect(injectionGrammar.injectTo).toContain('text.html.basic');
        expect(injectionGrammar.injectTo).toContain('text.html');
        expect(injectionGrammar.embeddedLanguages).toBeDefined();
        expect(injectionGrammar.embeddedLanguages['meta.embedded.inline.sline']).toBe('sline');
    });

    test('注入语法应该包含所有必要的 Sline 语法规则', () => {
        const grammar = JSON.parse(injectionGrammarContent);
        
        // 检查是否包含主要的 Sline 语法规则
        expect(grammar.repository.sline_content).toBeDefined();
        expect(grammar.repository.block_helpers).toBeDefined();
        expect(grammar.repository.end_blocks).toBeDefined();
        expect(grammar.repository.else_blocks).toBeDefined();
        expect(grammar.repository.variables_and_partials).toBeDefined();
        expect(grammar.repository.sline_strings).toBeDefined();
        expect(grammar.repository.filters).toBeDefined();
        expect(grammar.repository.variables).toBeDefined();
    });

    test('注入语法应该使用统一的标点符号作用域', () => {
        const grammar = JSON.parse(injectionGrammarContent);
        
        // 检查是否所有标点符号都使用统一的作用域
        const grammarString = JSON.stringify(grammar);
        const punctuationMatches = grammarString.match(/"punctuation\.definition\.tag\.sline"/g);
        
        expect(punctuationMatches).toBeTruthy();
        expect(punctuationMatches!.length).toBeGreaterThan(10); // 应该有多个地方使用这个作用域
    });

    test('注入语法应该使用 meta.embedded.inline.sline 作用域', () => {
        const grammar = JSON.parse(injectionGrammarContent);

        // 检查是否使用了正确的嵌入式作用域
        const mainPattern = grammar.patterns[0];
        expect(mainPattern.name).toBe('meta.embedded.inline.sline');
        expect(mainPattern.begin).toBe('\\{\\{');
        expect(mainPattern.end).toBe('\\}\\}');
    });

    test('注入语法应该正确处理字符串', () => {
        const grammar = JSON.parse(injectionGrammarContent);
        
        // 检查字符串处理规则
        const slineStrings = grammar.repository.sline_strings;
        expect(slineStrings.patterns).toHaveLength(2); // 双引号和单引号字符串
        
        const doubleQuotedString = slineStrings.patterns[0];
        expect(doubleQuotedString.name).toBe('string.quoted.double.sline');
        expect(doubleQuotedString.begin).toBe('"');
        expect(doubleQuotedString.end).toBe('"');
    });
});
