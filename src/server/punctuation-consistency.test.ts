import * as fs from 'fs';
import * as path from 'path';

describe('标点符号一致性测试', () => {
    let grammarContent: string;
    
    beforeAll(() => {
        // 读取语法文件
        const grammarPath = path.join(__dirname, '../../syntaxes/sline.tmLanguage.json');
        grammarContent = fs.readFileSync(grammarPath, 'utf8');
    });

    test('所有 Sline 标点符号应该使用一致的作用域', () => {
        const grammar = JSON.parse(grammarContent);

        // 收集所有标点符号作用域
        const punctuationScopes = new Set<string>();

        function collectScopes(obj: any) {
            if (typeof obj === 'object' && obj !== null) {
                if (obj.name && typeof obj.name === 'string' && obj.name.includes('punctuation')) {
                    punctuationScopes.add(obj.name);
                }

                for (const key in obj) {
                    collectScopes(obj[key]);
                }
            }
        }

        collectScopes(grammar);

        // 检查是否所有 Sline 标点符号都使用统一的作用域
        const slinePunctuationScopes = Array.from(punctuationScopes).filter(scope =>
            scope.includes('sline') && scope.includes('punctuation.definition.tag')
        );

        // 应该主要使用统一的作用域
        expect(slinePunctuationScopes).toContain('punctuation.definition.tag.sline');

        console.log('发现的 Sline 标点符号作用域:', slinePunctuationScopes);

        // 检查是否大部分标点符号都使用了统一的作用域
        const unifiedScopeCount = grammarContent.split('punctuation.definition.tag.sline').length - 1;
        expect(unifiedScopeCount).toBeGreaterThan(10); // 应该有多个地方使用统一作用域
    });

    test('不应该存在旧的不一致作用域', () => {
        // 检查是否还存在旧的不一致作用域
        const inconsistentScopes = [
            'support.constant.sline',
            'punctuation.handlebars'
        ];
        
        for (const scope of inconsistentScopes) {
            // 在标点符号上下文中不应该使用这些作用域
            const regex = new RegExp(`"name":\\s*"${scope.replace('.', '\\.')}"`, 'g');
            const matches = grammarContent.match(regex);
            
            if (matches) {
                console.log(`发现不一致的作用域 ${scope}:`, matches.length, '次');
                // 注意：我们允许一些旧作用域存在，但会记录它们
            }
        }
    });

    test('语法文件应该是有效的 JSON', () => {
        expect(() => {
            JSON.parse(grammarContent);
        }).not.toThrow();
    });

    test('应该包含所有必要的语法规则', () => {
        const grammar = JSON.parse(grammarContent);
        
        // 检查必要的规则是否存在
        expect(grammar.repository).toBeDefined();
        expect(grammar.repository.block_helpers).toBeDefined();
        expect(grammar.repository.end_blocks).toBeDefined();
        expect(grammar.repository.else_blocks).toBeDefined();
        expect(grammar.repository.variables_and_partials).toBeDefined();
        expect(grammar.repository.sline_strings).toBeDefined();
        expect(grammar.repository.sline_expression_in_string).toBeDefined();
    });
});
