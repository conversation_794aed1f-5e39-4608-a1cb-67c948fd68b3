// Enhanced Integration Tests for Sline Language Server
import { TextDocument } from 'vscode-languageserver-textdocument';
import { ContextAnalyzer } from './contextAnalysis';
import { ReferenceDataManager } from './referenceData';
import * as path from 'path';

describe('Enhanced Language Server Integration', () => {
    let referenceDataManager: ReferenceDataManager;

    beforeEach(async () => {
        const extensionPath = path.join(__dirname, '../../');
        referenceDataManager = new ReferenceDataManager(extensionPath);
        await referenceDataManager.loadReferenceData();
    });

    function createDocument(content: string): TextDocument {
        return TextDocument.create('test://test.sline', 'sline', 1, content);
    }

    describe('End-to-End Completion Scenarios', () => {
        test('should provide tag completions after {{#', () => {
            const doc = createDocument('{{# ');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 4 });
            
            expect(context.type).toBe('tag');
            
            const completions = referenceDataManager.getTagCompletions(context.prefix, context.needsClosingTag);
            expect(completions.length).toBeGreaterThan(0);
            
            // Should include intelligent closing tags
            const forCompletion = completions.find(item => item.label === 'for');
            expect(forCompletion?.insertText).toContain('{{/for}}');
        });

        test('should provide object property completions', () => {
            const doc = createDocument('{{ product.ti');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 13 });
            
            expect(context.type).toBe('object_property');
            expect(context.objectName).toBe('product');
            expect(context.prefix).toBe('ti');
            
            const completions = referenceDataManager.getObjectPropertyCompletions(
                context.objectName!, 
                context.prefix
            );
            
            expect(completions.some(item => item.label === 'title')).toBe(true);
        });

        test('should provide filter completions after pipe', () => {
            const doc = createDocument('{{ product.title | tr');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 21 });
            
            expect(context.type).toBe('filter');
            expect(context.prefix).toBe('tr');
            
            const completions = referenceDataManager.getFilterCompletions(context.prefix);
            expect(completions.some(item => item.label === 'truncate')).toBe(true);
        });

        test('should provide filter parameter completions', () => {
            const doc = createDocument('{{ product.price | money(');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 25 });
            
            expect(context.type).toBe('filter_parameter');
            expect(context.filterName).toBe('money');
            
            const completions = referenceDataManager.getFilterParameterCompletions(
                context.filterName!, 
                context.prefix
            );
            
            expect(completions.length).toBeGreaterThan(0);
        });
    });

    describe('Complex Expression Handling', () => {
        test('should handle nested object properties', () => {
            const doc = createDocument('{{ customer.default_address.ci');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 31 });
            
            expect(context.type).toBe('object_property');
            expect(context.objectName).toBe('default_address');
            expect(context.prefix).toBe('ci');
            
            const completions = referenceDataManager.getObjectPropertyCompletions(
                context.objectName!, 
                context.prefix
            );
            
            expect(completions.some(item => item.label === 'city')).toBe(true);
        });

        test('should handle filter chains', () => {
            const doc = createDocument('{{ product.tags | join(", ") | up');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 34 });
            
            expect(context.type).toBe('filter');
            expect(context.prefix).toBe('up');
            
            const completions = referenceDataManager.getFilterCompletions(context.prefix);
            expect(completions.some(item => item.label === 'upcase')).toBe(true);
        });

        test('should handle array access', () => {
            const doc = createDocument('{{ product.variants[0].pr');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 26 });
            
            expect(context.type).toBe('object_property');
            expect(context.prefix).toBe('pr');
        });
    });

    describe('Raw Value Support', () => {
        test('should handle raw value expressions', () => {
            const doc = createDocument('{{{ product.description');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 23 });
            
            // Should still provide meaningful context even in raw values
            expect(context.type).toBe('unknown'); // Raw values might have different handling
        });

        test('should handle raw values with whitespace control', () => {
            const doc = createDocument('{{{~ product.title ~}}}');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 15 });
            
            expect(context.type).toBe('unknown'); // In completed expression
        });
    });

    describe('Whitespace Control Support', () => {
        test('should handle whitespace control in tags', () => {
            const doc = createDocument('{{~ # for item in products ~}}');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 15 });
            
            expect(context.type).toBe('tag_parameter');
            expect(context.tagName).toBe('for');
        });

        test('should handle whitespace control in expressions', () => {
            const doc = createDocument('{{~ product.title | truncate(50) ~}}');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 20 });
            
            expect(context.type).toBe('filter');
        });
    });

    describe('Error Handling and Edge Cases', () => {
        test('should handle malformed expressions gracefully', () => {
            const doc = createDocument('{{ product..title');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 17 });
            
            // Should not crash and provide some context
            expect(context).toBeDefined();
        });

        test('should handle incomplete filter expressions', () => {
            const doc = createDocument('{{ product.title | ');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 19 });
            
            expect(context.type).toBe('filter');
            expect(context.prefix).toBe('');
        });

        test('should handle nested parentheses in filters', () => {
            const doc = createDocument('{{ image | image_url(width=calc(100 + 200), height=');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 52 });
            
            expect(context.type).toBe('filter_parameter');
            expect(context.filterName).toBe('image_url');
        });
    });

    describe('Performance Integration Tests', () => {
        test('should provide completions quickly for large documents', () => {
            const largeContent = Array(1000).fill('{{ product.title }}').join('\n');
            const doc = createDocument(largeContent + '\n{{ product.pr');
            
            const start = Date.now();
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { 
                line: 1000, 
                character: 13 
            });
            const completions = referenceDataManager.getObjectPropertyCompletions(
                context.objectName!, 
                context.prefix
            );
            const end = Date.now();
            
            expect(end - start).toBeLessThan(200); // Should complete in under 200ms
            expect(completions.length).toBeGreaterThan(0);
        });

        test('should handle rapid completion requests', () => {
            const doc = createDocument('{{ product.ti');
            
            const start = Date.now();
            for (let i = 0; i < 100; i++) {
                const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 13 });
                referenceDataManager.getObjectPropertyCompletions(context.objectName!, context.prefix);
            }
            const end = Date.now();
            
            expect(end - start).toBeLessThan(1000); // 100 requests in under 1 second
        });
    });

    describe('HTML Integration Scenarios', () => {
        test('should work in HTML attribute context', () => {
            const doc = createDocument('<div class="{{ product.ti');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 26 });
            
            expect(context.type).toBe('object_property');
            expect(context.objectName).toBe('product');
            expect(context.prefix).toBe('ti');
        });

        test('should work in HTML content context', () => {
            const doc = createDocument('<h1>{{ product.ti</h1>');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 17 });
            
            expect(context.type).toBe('object_property');
            expect(context.objectName).toBe('product');
            expect(context.prefix).toBe('ti');
        });

        test('should handle mixed HTML and Sline', () => {
            const doc = createDocument(`
                <div class="product">
                    {{# if product.available }}
                        <span>{{ product.pr
                    {{/ if }}
                </div>
            `);
            
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 3, character: 33 });
            
            expect(context.type).toBe('object_property');
            expect(context.objectName).toBe('product');
            expect(context.prefix).toBe('pr');
        });
    });
});
