// Enhanced Context Analysis Tests
import { ContextAnalyzer } from './contextAnalysis';
import { TextDocument } from 'vscode-languageserver-textdocument';

describe('Enhanced ContextAnalyzer', () => {
    
    function createDocument(content: string): TextDocument {
        return TextDocument.create('test://test.sline', 'sline', 1, content);
    }

    describe('Raw Value Detection', () => {
        test('should detect raw value context', () => {
            const doc = createDocument('{{{ product.description ');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 23 });
            
            expect(context.type).toBe('unknown'); // Should be in raw value context
        });

        test('should handle raw value with whitespace control', () => {
            const doc = createDocument('{{{~ product.title ');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 19 });
            
            expect(context.type).toBe('unknown'); // Should be in raw value context
        });
    });

    describe('Enhanced Tag Detection', () => {
        test('should detect tag completion with closing tag needed', () => {
            const doc = createDocument('{{# ');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 4 });
            
            expect(context.type).toBe('tag');
            expect(context.prefix).toBe('');
            expect(context.needsClosingTag).toBe(true);
        });

        test('should detect tag name completion', () => {
            const doc = createDocument('{{# fo');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 6 });
            
            expect(context.type).toBe('tag');
            expect(context.prefix).toBe('fo');
        });

        test('should detect tag parameter completion', () => {
            const doc = createDocument('{{# for item in ');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 16 });
            
            expect(context.type).toBe('tag_parameter');
            expect(context.tagName).toBe('for');
            expect(context.prefix).toBe('item in ');
        });
    });

    describe('Enhanced Object Property Detection', () => {
        test('should detect object property access', () => {
            const doc = createDocument('{{ product.ti');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 13 });
            
            expect(context.type).toBe('object_property');
            expect(context.objectName).toBe('product');
            expect(context.prefix).toBe('ti');
        });

        test('should detect nested object property access', () => {
            const doc = createDocument('{{ customer.default_address.ci');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 31 });
            
            expect(context.type).toBe('object_property');
            expect(context.objectName).toBe('default_address');
            expect(context.prefix).toBe('ci');
        });

        test('should detect object access start', () => {
            const doc = createDocument('{{ product.');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 11 });
            
            expect(context.type).toBe('object_property');
            expect(context.objectName).toBe('product');
            expect(context.prefix).toBe('');
        });
    });

    describe('Enhanced Filter Detection', () => {
        test('should detect filter completion', () => {
            const doc = createDocument('{{ product.title | tr');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 21 });
            
            expect(context.type).toBe('filter');
            expect(context.prefix).toBe('tr');
        });

        test('should detect filter parameter completion', () => {
            const doc = createDocument('{{ product.price | money(');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 25 });
            
            expect(context.type).toBe('filter_parameter');
            expect(context.filterName).toBe('money');
            expect(context.prefix).toBe('');
        });

        test('should detect filter hash parameter completion', () => {
            const doc = createDocument('{{ image | image_url(width=300, height=');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 40 });
            
            expect(context.type).toBe('filter_parameter');
            expect(context.filterName).toBe('image_url');
        });

        test('should handle complex filter chains', () => {
            const doc = createDocument('{{ product.tags | join(", ") | up');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 34 });
            
            expect(context.type).toBe('filter');
            expect(context.prefix).toBe('up');
        });
    });

    describe('Whitespace Control Detection', () => {
        test('should handle whitespace control in tags', () => {
            const doc = createDocument('{{~ # for item in products ~}}');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 15 });
            
            expect(context.type).toBe('tag_parameter');
            expect(context.tagName).toBe('for');
        });

        test('should handle whitespace control in expressions', () => {
            const doc = createDocument('{{~ product.title | truncate(50) ~}}');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 20 });
            
            expect(context.type).toBe('filter');
        });
    });

    describe('Complex Expression Detection', () => {
        test('should handle array access', () => {
            const doc = createDocument('{{ product.variants[0].pr');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 26 });
            
            expect(context.type).toBe('object_property');
            expect(context.prefix).toBe('pr');
        });

        test('should handle conditional expressions', () => {
            const doc = createDocument('{{# if product.price > 100 && product.av');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 41 });
            
            expect(context.type).toBe('object_property');
            expect(context.objectName).toBe('product');
            expect(context.prefix).toBe('av');
        });

        test('should handle boolean literals', () => {
            const doc = createDocument('{{# if product.available == tr');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 30 });
            
            // Should detect literal completion context
            expect(context.type).toBe('tag_parameter');
        });
    });

    describe('Comment Detection', () => {
        test('should not provide completion in comments', () => {
            const doc = createDocument('{{! This is a comment with product.');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 35 });
            
            expect(context.type).toBe('unknown');
        });

        test('should not provide completion in block comments', () => {
            const doc = createDocument('{{!-- Block comment with product.title --}}');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 30 });
            
            expect(context.type).toBe('unknown');
        });
    });

    describe('Edge Cases', () => {
        test('should handle incomplete expressions', () => {
            const doc = createDocument('{{ ');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 3 });
            
            expect(context.type).toBe('unknown');
            expect(context.prefix).toBe('');
        });

        test('should handle malformed expressions', () => {
            const doc = createDocument('{{ product..title');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 17 });
            
            // Should still try to provide meaningful context
            expect(context.type).toBe('unknown');
        });

        test('should handle nested braces', () => {
            const doc = createDocument('{{ product.variants[variant_index].price }}');
            const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 30 });
            
            expect(context.type).toBe('object_property');
        });
    });
});
