import { ReferenceDataManager } from './referenceData';
import * as path from 'path';

describe('语法高亮修复测试', () => {
    let referenceDataManager: ReferenceDataManager;

    beforeEach(async () => {
        // 创建 ReferenceDataManager 实例
        const dataPath = path.join(__dirname, '..');
        referenceDataManager = new ReferenceDataManager(dataPath);
        
        // 加载参考数据
        await referenceDataManager.loadReferenceData();
    });

    test('应该正确识别 HTML 属性中的 Sline 表达式', () => {
        // 测试 HTML 属性中的 Sline 表达式不会导致语法错误
        const htmlWithSline = `<a href="{{ article.url | default('javascript:void(0)') }}" class="test {{#if condition}}active{{/if}}">`;
        
        // 这里我们主要测试标签识别功能
        expect(referenceDataManager.hasTag('if')).toBe(true);
    });

    test('应该正确识别字符串中的引号', () => {
        // 测试字符串处理
        const complexString = `<div data-config='{"key": "value", "nested": "{{product.title}}"}'>`;
        
        // 验证相关标签和过滤器存在
        expect(referenceDataManager.hasTag('if')).toBe(true);
        expect(referenceDataManager.hasFilter('default')).toBe(true);
    });

    test('应该支持嵌套的 Sline 表达式', () => {
        // 测试嵌套表达式
        const nestedExpression = `{{#if settings.article_card_style == "card"}}article-card-border-shadow{{/if}}`;
        
        // 验证条件标签存在
        expect(referenceDataManager.hasTag('if')).toBe(true);
    });

    test('应该正确处理自闭合标签', () => {
        // 测试自闭合标签
        expect(referenceDataManager.hasTag('placeholder_svg')).toBe(true);
        expect(referenceDataManager.hasTag('block')).toBe(true);
    });

    test('应该正确处理过滤器', () => {
        // 测试过滤器识别
        expect(referenceDataManager.hasFilter('default')).toBe(true);
        expect(referenceDataManager.hasFilter('class_list')).toBe(true);
    });
});
