// Enhanced Reference Data Manager Tests
import { ReferenceDataManager } from './referenceData';
import { CompletionItemKind } from 'vscode-languageserver/node';
import * as path from 'path';

describe('Enhanced ReferenceDataManager', () => {
    let manager: ReferenceDataManager;

    beforeEach(async () => {
        // Use the actual extension path for testing
        const extensionPath = path.join(__dirname, '../../');
        manager = new ReferenceDataManager(extensionPath);
        await manager.loadReferenceData();
    });

    describe('Enhanced Tag Completion', () => {
        test('should provide basic tag completions', () => {
            const completions = manager.getTagCompletions('');
            
            expect(completions.length).toBeGreaterThan(0);
            expect(completions.some(item => item.label === 'for')).toBe(true);
            expect(completions.some(item => item.label === 'if')).toBe(true);
        });

        test('should filter tag completions by prefix', () => {
            const completions = manager.getTagCompletions('fo');
            
            expect(completions.every(item => 
                item.label.toLowerCase().startsWith('fo')
            )).toBe(true);
        });

        test('should provide intelligent closing tags for block tags', () => {
            const completions = manager.getTagCompletions('for', true);
            const forCompletion = completions.find(item => item.label === 'for');
            
            expect(forCompletion).toBeDefined();
            expect(forCompletion?.insertText).toContain('for}$1{{/for}');
            expect(forCompletion?.insertTextFormat).toBe(2); // Snippet format
        });

        test('should not add closing tags for self-closing tags', () => {
            const completions = manager.getTagCompletions('payment_type_svg', true);
            const svgCompletion = completions.find(item => item.label === 'payment_type_svg');
            
            expect(svgCompletion).toBeDefined();
            // Self-closing tags should not have auto-closing
            expect(svgCompletion?.insertText).not.toContain('{{/payment_type_svg}}');
        });

        test('should provide detailed documentation for tags', () => {
            const completions = manager.getTagCompletions('for');
            const forCompletion = completions.find(item => item.label === 'for');
            
            expect(forCompletion).toBeDefined();
            expect(forCompletion?.documentation).toBeDefined();
            expect(forCompletion?.detail).toContain('Sline Tag');
        });
    });

    describe('Enhanced Filter Completion', () => {
        test('should provide filter completions', () => {
            const completions = manager.getFilterCompletions('');
            
            expect(completions.length).toBeGreaterThan(0);
            expect(completions.some(item => item.label === 'money')).toBe(true);
            expect(completions.some(item => item.label === 'truncate')).toBe(true);
        });

        test('should filter completions by prefix', () => {
            const completions = manager.getFilterCompletions('mon');
            
            expect(completions.every(item => 
                item.label.toLowerCase().startsWith('mon')
            )).toBe(true);
        });

        test('should provide filter documentation', () => {
            const completions = manager.getFilterCompletions('money');
            const moneyCompletion = completions.find(item => item.label === 'money');
            
            expect(moneyCompletion).toBeDefined();
            expect(moneyCompletion?.documentation).toBeDefined();
            expect(moneyCompletion?.kind).toBe(CompletionItemKind.Function);
        });
    });

    describe('Enhanced Object Property Completion', () => {
        test('should provide object property completions', () => {
            const completions = manager.getObjectPropertyCompletions('product', '');
            
            expect(completions.length).toBeGreaterThan(0);
            expect(completions.some(item => item.label === 'title')).toBe(true);
            expect(completions.some(item => item.label === 'price')).toBe(true);
        });

        test('should filter property completions by prefix', () => {
            const completions = manager.getObjectPropertyCompletions('product', 'ti');
            
            expect(completions.every(item => 
                item.label.toLowerCase().startsWith('ti')
            )).toBe(true);
        });

        test('should handle unknown objects gracefully', () => {
            const completions = manager.getObjectPropertyCompletions('unknown_object', '');
            
            expect(completions).toEqual([]);
        });

        test('should provide property documentation', () => {
            const completions = manager.getObjectPropertyCompletions('product', 'title');
            const titleCompletion = completions.find(item => item.label === 'title');
            
            expect(titleCompletion).toBeDefined();
            expect(titleCompletion?.documentation).toBeDefined();
        });
    });

    describe('Tag Parameter Completion', () => {
        test('should provide tag parameter completions', () => {
            const completions = manager.getTagParameterCompletions('for', '');

            // Tag parameter completions might be empty if not implemented yet
            expect(completions).toBeDefined();
            expect(Array.isArray(completions)).toBe(true);
        });

        test('should handle unknown tags gracefully', () => {
            const completions = manager.getTagParameterCompletions('unknown_tag', '');
            
            expect(completions).toEqual([]);
        });
    });

    describe('Filter Parameter Completion', () => {
        test('should provide filter parameter completions', () => {
            const completions = manager.getFilterParameterCompletions('money', '');

            // Filter parameter completions might be empty if not implemented yet
            expect(completions).toBeDefined();
            expect(Array.isArray(completions)).toBe(true);
        });

        test('should handle unknown filters gracefully', () => {
            const completions = manager.getFilterParameterCompletions('unknown_filter', '');
            
            expect(completions).toEqual([]);
        });
    });

    describe('Block Tag Detection', () => {
        test('should correctly identify block tags', () => {
            const completions = manager.getTagCompletions('for', true);
            const forCompletion = completions.find(item => item.label === 'for');
            
            expect(forCompletion?.insertText).toContain('{{/for}}');
        });

        test('should correctly identify self-closing tags', () => {
            const completions = manager.getTagCompletions('format_address', true);
            const addressCompletion = completions.find(item => item.label === 'format_address');
            
            // Self-closing tags should not have closing tags
            expect(addressCompletion?.insertText).not.toContain('{{/format_address}}');
        });
    });

    describe('Data Loading and Validation', () => {
        test('should load tag data successfully', () => {
            expect(manager.getTagCompletions('').length).toBeGreaterThan(0);
        });

        test('should load filter data successfully', () => {
            expect(manager.getFilterCompletions('').length).toBeGreaterThan(0);
        });

        test('should load object data successfully', () => {
            expect(manager.getObjectPropertyCompletions('product', '').length).toBeGreaterThan(0);
        });

        test('should handle missing data files gracefully', async () => {
            const invalidManager = new ReferenceDataManager('/invalid/path');
            
            // Should not throw an error
            await expect(invalidManager.loadReferenceData()).resolves.not.toThrow();
        });
    });

    describe('Performance Tests', () => {
        test('should provide completions quickly', () => {
            const start = Date.now();
            manager.getTagCompletions('');
            const end = Date.now();
            
            expect(end - start).toBeLessThan(100); // Should complete in under 100ms
        });

        test('should handle large prefix filtering efficiently', () => {
            const start = Date.now();
            manager.getFilterCompletions('very_long_prefix_that_matches_nothing');
            const end = Date.now();
            
            expect(end - start).toBeLessThan(50); // Should complete quickly even with no matches
        });
    });

    describe('Completion Item Quality', () => {
        test('should provide meaningful completion items', () => {
            const completions = manager.getTagCompletions('for');
            const forCompletion = completions.find(item => item.label === 'for');
            
            expect(forCompletion).toBeDefined();
            expect(forCompletion?.label).toBe('for');
            expect(forCompletion?.kind).toBe(CompletionItemKind.Function);
            expect(forCompletion?.detail).toContain('Sline Tag');
            expect(forCompletion?.documentation).toBeDefined();
        });

        test('should provide consistent completion item structure', () => {
            const completions = manager.getTagCompletions('');
            
            completions.forEach(item => {
                expect(item.label).toBeDefined();
                expect(item.kind).toBeDefined();
                expect(item.detail).toBeDefined();
            });
        });
    });
});
