import { ReferenceDataManager } from './referenceData';
import * as path from 'path';

describe('placeholder_svg 标签测试', () => {
    let referenceDataManager: ReferenceDataManager;

    beforeEach(async () => {
        // 创建 ReferenceDataManager 实例
        const dataPath = path.join(__dirname, '..');
        referenceDataManager = new ReferenceDataManager(dataPath);

        // 加载参考数据
        await referenceDataManager.loadReferenceData();
    });

    test('应该识别 placeholder_svg 标签', () => {
        // 测试 placeholder_svg 标签是否被正确识别
        expect(referenceDataManager.hasTag('placeholder_svg')).toBe(true);
    });

    test('应该识别其他新添加的标签', () => {
        // 测试其他在 supplementMissingTags 中新添加的标签
        const newTags = [
            'payment_type_svg',
            'company_account_application_form',
            'contact_form',
            'customer_form',
            'customer_login_link',
            'customer_logout_link',
            'customer_register_link',
            'customer_subscribe_form',
            'customer_unsubscribe_form',
            'delete_customer_form',
            'format_address',
            'link_to_customer_login',
            'link_to_customer_logout',
            'link_to_customer_register',
            'localization_form',
            'new_comment_form',
            'order_tracking_form',
            'storefront_password_form',
            'style',
            'switch',
            'update_customer_form'
        ];

        newTags.forEach(tagName => {
            expect(referenceDataManager.hasTag(tagName)).toBe(true);
        });
    });

    test('应该为 placeholder_svg 提供相似标签建议', () => {
        // 测试拼写错误时的建议功能
        const suggestions = referenceDataManager.getSimilarTags('placeholder');
        expect(suggestions).toContain('placeholder_svg');
    });

    test('应该为 placeholder_svg 提供完成项', () => {
        const completionItems = referenceDataManager.getTagCompletions('placeholder');
        const placeholderSvgItem = completionItems.find((item: any) => item.label === 'placeholder_svg');
        expect(placeholderSvgItem).toBeDefined();
        expect(placeholderSvgItem?.detail).toContain('Sline Tag');
    });
});
