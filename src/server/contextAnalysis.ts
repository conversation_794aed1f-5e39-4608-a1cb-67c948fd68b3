// SLine 语言服务器上下文分析模块
import { TextDocument, Position } from 'vscode-languageserver-textdocument';
import { CompletionContext } from './referenceData';

/**
 * 分析完成上下文的工具类
 */
export class ContextAnalyzer {
    
    /**
     * 分析给定位置的完成上下文
     */
    static analyzeCompletionContext(document: TextDocument, position: Position): CompletionContext {
        const text = document.getText();
        const offset = document.offsetAt(position);
        const beforeCursor = text.substring(0, offset);
        const afterCursor = text.substring(offset);

        // 查找最近的 Sline 表达式开始位置（支持 {{ 和 {{{ ）
        const lastOpenBrace = this.findLastOpenBrace(beforeCursor);
        const lastCloseBrace = this.findLastCloseBrace(beforeCursor);

        // 如果最近的闭合标签在开放标签之后，说明不在表达式内
        if (lastCloseBrace.position > lastOpenBrace.position) {
            return { type: 'unknown', prefix: '' };
        }

        // 如果没有找到开放标签，说明不在表达式内
        if (lastOpenBrace.position === -1) {
            return { type: 'unknown', prefix: '' };
        }

        // 检查光标后面是否紧跟着 }}，这可能表示用户在自动完成的 {{}} 中间输入
        const nextCloseBrace = afterCursor.indexOf('}}');
        const isInAutoCompletedBraces = nextCloseBrace === 0; // 光标紧跟着 }}

        // 获取表达式内容（从开放标签到光标位置）
        const expressionContent = beforeCursor.substring(lastOpenBrace.position + lastOpenBrace.length);
        const isRawValue = lastOpenBrace.type === 'raw';

        // 智能处理标签输入场景
        if (expressionContent.startsWith('#')) {
            // 检查是否刚输入了 #
            if (expressionContent === '#') {
                return {
                    type: 'tag',
                    prefix: '',
                    needsClosingTag: true
                };
            }
            // 检查是否在输入标签名
            if (expressionContent.length > 1) {
                return {
                    type: 'tag',
                    prefix: expressionContent.substring(1)
                };
            }
            // 如果表达式为空，返回 unknown
            if (expressionContent.trim() === '') {
                return {
                    type: 'unknown',
                    prefix: ''
                };
            }
        }

        // 分析表达式内容
        return this.analyzeExpressionContent(expressionContent);
    }
    
    /**
     * 分析表达式内容以确定上下文类型
     */
    private static analyzeExpressionContent(content: string): CompletionContext {
        const trimmedContent = content.trim();

        // 检查是否在标签参数中（例如：{{#for item in products}}）
        if (trimmedContent.startsWith('#')) {
            const tagMatch = trimmedContent.match(/^#([a-zA-Z_][a-zA-Z0-9_]*)\s+(.*)$/);
            if (tagMatch) {
                return {
                    type: 'tag_parameter',
                    prefix: tagMatch[2].trim(),
                    tagName: tagMatch[1]
                };
            }
        }

        // 检查对象属性访问 (例如: "product.title", "customer.first_name", "product.ti")
        const propertyMatch = trimmedContent.match(/([a-zA-Z_][a-zA-Z0-9_]*)\.([a-zA-Z_][a-zA-Z0-9_]*)$/);
        if (propertyMatch) {
            return {
                type: 'object_property',
                prefix: propertyMatch[2],
                objectName: propertyMatch[1]
            };
        }
        
        // 检查对象访问开始 (例如: "product.")
        const objectAccessMatch = trimmedContent.match(/([a-zA-Z_][a-zA-Z0-9_]*)\.$/);
        if (objectAccessMatch) {
            return {
                type: 'object_property',
                prefix: '',
                objectName: objectAccessMatch[1]
            };
        }
        
        // 检查过滤器上下文 (管道符后面)
        const pipeIndex = ContextAnalyzer.findLastPipeIndex(trimmedContent);
        if (pipeIndex !== -1) {
            const afterPipe = trimmedContent.substring(pipeIndex + 1).trim();
            
            // 检查过滤器参数 (例如: "| money(code=")
            const filterParamMatch = afterPipe.match(/([a-zA-Z_][a-zA-Z0-9_]*)\s*\(\s*([a-zA-Z_][a-zA-Z0-9_]*=?)?$/);
            if (filterParamMatch) {
                return {
                    type: 'filter_parameter',
                    prefix: filterParamMatch[2] || '',
                    filterName: filterParamMatch[1]
                };
            }
            
            // 检查过滤器名称
            const filterMatch = afterPipe.match(/^([a-zA-Z_][a-zA-Z0-9_]*)$/);
            if (filterMatch) {
                return {
                    type: 'filter',
                    prefix: filterMatch[1]
                };
            }
            
            // 管道符后面但没有匹配到具体模式
            return {
                type: 'filter',
                prefix: afterPipe
            };
        }
        
        // 检查标签上下文 (# 开头)
        if (trimmedContent.startsWith('#')) {
            const tagContent = trimmedContent.substring(1);
            
            // 检查标签参数 (例如: "#for item in", "#if condition")
            const tagParamMatch = tagContent.match(/^([a-zA-Z_][a-zA-Z0-9_]*)\s+(.*)$/);
            if (tagParamMatch) {
                const tagName = tagParamMatch[1];
                const paramContent = tagParamMatch[2];
                
                // 检查是否在参数名位置
                const paramMatch = paramContent.match(/([a-zA-Z_][a-zA-Z0-9_]*=?)?$/);
                if (paramMatch) {
                    return {
                        type: 'tag_parameter',
                        prefix: paramMatch[1] || '',
                        tagName: tagName
                    };
                }
            }
            
            // 检查标签名称
            const tagNameMatch = tagContent.match(/^([a-zA-Z_][a-zA-Z0-9_]*)$/);
            if (tagNameMatch) {
                return {
                    type: 'tag',
                    prefix: tagNameMatch[1]
                };
            }
            
            // # 后面但没有匹配到具体模式
            return {
                type: 'tag',
                prefix: tagContent
            };
        }
        
        // 检查是否在变量或对象名称位置
        const variableMatch = trimmedContent.match(/^([a-zA-Z_][a-zA-Z0-9_]*)$/);
        if (variableMatch) {
            // 这是一个简单的变量引用，不是对象属性访问
            return {
                type: 'unknown', // 简单变量引用
                prefix: variableMatch[1]
            };
        }
        
        // 表达式开始位置，提供通用建议
        if (trimmedContent === '') {
            return {
                type: 'tag', // 默认提供标签建议
                prefix: ''
            };
        }
        
        return { type: 'unknown', prefix: trimmedContent };
    }
    
    /**
     * 获取光标位置的单词范围
     */
    static getWordRangeAtPosition(document: TextDocument, position: Position): { start: Position; end: Position } {
        const text = document.getText();
        const offset = document.offsetAt(position);
        
        // 查找单词边界
        let start = offset;
        let end = offset;
        
        // 向前查找单词开始
        while (start > 0 && this.isWordCharacter(text.charAt(start - 1))) {
            start--;
        }
        
        // 向后查找单词结束
        while (end < text.length && this.isWordCharacter(text.charAt(end))) {
            end++;
        }
        
        return {
            start: document.positionAt(start),
            end: document.positionAt(end)
        };
    }
    
    /**
     * 检查字符是否是单词字符
     */
    private static isWordCharacter(char: string): boolean {
        return /[a-zA-Z0-9_]/.test(char);
    }
    
    /**
     * 检查位置是否在 Sline 表达式内
     */
    static isInSlineExpression(document: TextDocument, position: Position): boolean {
        const text = document.getText();
        const offset = document.offsetAt(position);
        const beforeCursor = text.substring(0, offset);
        
        const lastOpenBrace = beforeCursor.lastIndexOf('{{');
        const lastCloseBrace = beforeCursor.lastIndexOf('}}');
        
        // 如果最近的 {{ 在最近的 }} 之后，说明在表达式内
        return lastOpenBrace > lastCloseBrace;
    }
    
    /**
     * 获取当前表达式的完整内容
     */
    static getCurrentExpression(document: TextDocument, position: Position): string | null {
        const text = document.getText();
        const offset = document.offsetAt(position);
        
        // 向前查找 {{
        let start = offset;
        while (start >= 0 && text.substring(start, start + 2) !== '{{') {
            start--;
        }
        
        if (start < 0) {
            return null;
        }
        
        // 向后查找 }}
        let end = offset;
        while (end < text.length - 1 && text.substring(end, end + 2) !== '}}') {
            end++;
        }
        
        if (end >= text.length - 1) {
            // 没有找到结束标记，可能是未完成的表达式
            return text.substring(start + 2);
        }
        
        return text.substring(start + 2, end);
    }
    
    /**
     * 检查是否在标签内部
     */
    static isInTag(document: TextDocument, position: Position): { inTag: boolean; tagName?: string } {
        const expression = this.getCurrentExpression(document, position);
        if (!expression) {
            return { inTag: false };
        }
        
        const trimmed = expression.trim();
        if (trimmed.startsWith('#')) {
            const tagMatch = trimmed.match(/^#([a-zA-Z_][a-zA-Z0-9_]*)/);
            if (tagMatch) {
                return { inTag: true, tagName: tagMatch[1] };
            }
        }
        
        return { inTag: false };
    }
    
    /**
     * 检查是否在过滤器链中
     */
    static isInFilterChain(document: TextDocument, position: Position): { inFilter: boolean; filterName?: string } {
        const expression = this.getCurrentExpression(document, position);
        if (!expression) {
            return { inFilter: false };
        }
        
        const pipeIndex = ContextAnalyzer.findLastPipeIndex(expression);
        if (pipeIndex !== -1) {
            const afterPipe = expression.substring(pipeIndex + 1).trim();
            const filterMatch = afterPipe.match(/^([a-zA-Z_][a-zA-Z0-9_]*)/);
            if (filterMatch) {
                return { inFilter: true, filterName: filterMatch[1] };
            }
            return { inFilter: true };
        }
        
        return { inFilter: false };
    }
    
    /**
     * 分析嵌套上下文（例如在 for 循环内）
     */
    static analyzeNestedContext(document: TextDocument, position: Position): {
        inLoop: boolean;
        loopVariable?: string;
        inConditional: boolean;
        availableVariables: string[];
    } {
        const text = document.getText();
        const currentOffset = document.offsetAt(position);
        
        const result = {
            inLoop: false,
            loopVariable: undefined as string | undefined,
            inConditional: false,
            availableVariables: [] as string[]
        };
        
        // 查找所有的标签来分析嵌套上下文
        const tagRegex = /\{\{#(for|if|unless|with)\s+([^}]+)\}\}/g;
        let match;
        
        while ((match = tagRegex.exec(text)) !== null) {
            const tagStart = match.index;

            if (tagStart < currentOffset) {
                const tagType = match[1];
                const tagContent = match[2];
                
                if (tagType === 'for') {
                    // 解析 for 循环变量
                    const forMatch = tagContent.match(/(\w+)\s+in\s+(\w+)/);
                    if (forMatch) {
                        result.inLoop = true;
                        result.loopVariable = forMatch[1];
                        result.availableVariables.push(forMatch[1]);
                        result.availableVariables.push('forloop');
                    }
                } else if (tagType === 'if' || tagType === 'unless') {
                    result.inConditional = true;
                } else if (tagType === 'with') {
                    // 解析 with 上下文变量
                    const withMatch = tagContent.match(/(\w+)\s+as\s+(\w+)/);
                    if (withMatch) {
                        result.availableVariables.push(withMatch[2]);
                    }
                }
            }
        }
        
        return result;
    }

    /**
     * 查找最后一个真正的管道符位置，排除逻辑或运算符 ||
     */
    public static findLastPipeIndex(content: string): number {
        let lastPipeIndex = -1;

        for (let i = content.length - 1; i >= 0; i--) {
            if (content[i] === '|') {
                // 检查是否是逻辑或运算符 ||
                const prevChar = i > 0 ? content[i - 1] : '';
                const nextChar = i < content.length - 1 ? content[i + 1] : '';

                // 如果前一个字符或后一个字符也是 |，则这是逻辑或运算符，跳过
                if (prevChar === '|' || nextChar === '|') {
                    continue;
                }

                // 这是一个真正的管道符
                lastPipeIndex = i;
                break;
            }
        }

        return lastPipeIndex;
    }

    /**
     * 查找最后一个开放标签位置（支持 {{ 和 {{{ ）
     */
    private static findLastOpenBrace(text: string): { position: number; length: number; type: 'normal' | 'raw' } {
        const normalBrace = text.lastIndexOf('{{');
        const rawBrace = text.lastIndexOf('{{{');

        // 检查是否是原始值标签
        if (rawBrace !== -1 && rawBrace > normalBrace) {
            return { position: rawBrace, length: 3, type: 'raw' };
        }

        // 检查是否是普通标签，但不是原始值标签的一部分
        if (normalBrace !== -1 && (rawBrace === -1 || normalBrace > rawBrace + 1)) {
            return { position: normalBrace, length: 2, type: 'normal' };
        }

        return { position: -1, length: 0, type: 'normal' };
    }

    /**
     * 查找最后一个闭合标签位置（支持 }} 和 }}} ）
     */
    private static findLastCloseBrace(text: string): { position: number; length: number; type: 'normal' | 'raw' } {
        const normalBrace = text.lastIndexOf('}}');
        const rawBrace = text.lastIndexOf('}}}');

        // 检查是否是原始值闭合标签
        if (rawBrace !== -1 && rawBrace + 1 > normalBrace) {
            return { position: rawBrace + 1, length: 3, type: 'raw' };
        }

        // 检查是否是普通闭合标签
        if (normalBrace !== -1) {
            return { position: normalBrace, length: 2, type: 'normal' };
        }

        return { position: -1, length: 0, type: 'normal' };
    }
}
