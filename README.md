# SLine Language Support

[![Visual Studio Marketplace Version](https://img.shields.io/visual-studio-marketplace/v/your-publisher-name.sline-highlight)](https://marketplace.visualstudio.com/items?itemName=your-publisher-name.sline-highlight)
[![Visual Studio Marketplace Downloads](https://img.shields.io/visual-studio-marketplace/d/your-publisher-name.sline-highlight)](https://marketplace.visualstudio.com/items?itemName=your-publisher-name.sline-highlight)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

[tag.json](https://cdn-theme.myshopline.com/cdn/lex/tag.json)
[filter.json](https://cdn-theme.myshopline.com/cdn/lex/filter.json)
[objects.json](https://cdn-theme.myshopline.com/cdn/lex/objects.json)

**Professional Sline template engine support for Visual Studio Code.** Transform your template development experience with intelligent syntax highlighting, comprehensive IntelliSense, and advanced language server features.

> 🚀 **Latest Update**: Powerful intelligent language server with 20+ tags, 80+ filters, and 15+ objects. Features context-aware code completion, hover documentation, parameter hints, and smart error detection.

## ✨ Why Choose SLine Language Support?

Transform your Sline template development with enterprise-grade language features:

### 🎯 **Intelligent Code Completion**
- **Context-aware suggestions** - Smart autocomplete for tags, filters, and object properties
- **20+ Sline tags** - Complete support for `for`, `if`, `component`, `layout`, and more
- **80+ filter functions** - Instant access to `money`, `date`, `image_url`, and all Sline filters
- **15+ core objects** - IntelliSense for `product`, `cart`, `customer`, `shop` properties

### 🎨 **Advanced Syntax Highlighting**
- **Complete Sline syntax support** - Full Handlebars-style template highlighting
- **Consistent visual experience** - Unified colors for comments and punctuation
- **Multi-file support** - Works seamlessly with `.sline` and `.html` files

### 🧠 **Smart Language Server**
- **Instant documentation** - Hover over any tag or filter for complete syntax help
- **Parameter assistance** - Real-time parameter hints with type information
- **Error detection** - Intelligent validation with spelling suggestions
- **Performance optimized** - Millisecond response times, even with large files

### 📝 **Productivity Boosters**
- **15+ code snippets** - Pre-built templates for forms, layouts, and common patterns
- **Smart language switching** - Auto-detection of Sline syntax in HTML files
- **Tab navigation** - Intelligent placeholder jumping in snippets
- **Deprecation warnings** - Stay up-to-date with latest Sline best practices

## 🚀 Installation

### From VS Code Marketplace

1. Open Visual Studio Code
2. Press `Ctrl+Shift+X` (Windows/Linux) or `Cmd+Shift+X` (Mac) to open Extensions panel
3. Search for **"SLine Language Support"**
4. Click **Install**

### From Command Line

```bash
code --install-extension your-publisher-name.sline-highlight
```

## ⚡ Quick Start

Get started in seconds and experience the power of intelligent Sline development:

### 1. 🎯 Smart Code Completion

Create a `.sline` file or open an HTML file with Sline syntax:

```sline
<!-- Type {{# to see intelligent tag suggestions -->
{{#fo
<!-- Suggests 'for' with complete syntax and examples -->

<!-- Type object. to see available properties -->
{{ product.ti
<!-- Suggests 'title', 'type', and other product properties -->

<!-- Type | to see filter suggestions -->
{{ product.price | mon
<!-- Suggests 'money', 'money_with_currency', etc. -->
```

### 2. 📖 Instant Documentation

Hover over any Sline element to see:
- Complete syntax documentation with examples
- Parameter types and descriptions
- Links to official documentation
- Usage recommendations and best practices

### 3. 📋 Parameter Assistance

```sline
<!-- Get real-time parameter hints as you type -->
{{#for item in products limit:
<!-- Shows all available parameters for the 'for' tag -->

<!-- See parameter types for filters -->
{{ price | money(currency=
<!-- Displays currency parameter type and description -->
```

### 4. 📝 Code Snippets

Type snippet prefixes and press Tab:
- `for` → Complete for loop structure
- `if` → Conditional block with else
- `component` → Component call template
- `layout` → Basic layout template

**That's it!** Start coding with intelligent Sline support 🎉

## 📖 Features Deep Dive

### 🎯 Smart Language Mode Switching

**Automatic Detection (Recommended)**
1. Open any `.html` file containing Sline syntax in VS Code
2. The extension automatically detects Sline patterns and suggests switching to Sline mode
3. Click "Switch" or "Always Auto-Switch" to enable full Sline support

**Manual Switching**
1. Open your template file
2. Press `Ctrl+Shift+P` (Windows/Linux) or `Cmd+Shift+P` (Mac)
3. Type "SLine" and select "Switch to SLine Language Mode"
4. Or click the language mode in the status bar and select "SLine"

**Native .sline Files**
Create `.sline` files directly - all features activate automatically.

### ⚙️ Configuration

Customize your experience in VS Code settings (search for "sline"):

```json
{
  "slineLanguageServer.autoSwitchMode": false,        // Auto-switch language mode
  "slineLanguageServer.showModeSuggestion": true,     // Show switch suggestions
  "slineLanguageServer.enableDiagnostics": true,      // Enable syntax validation
  "slineLanguageServer.enableCompletion": true        // Enable code completion
}
```

### 🔧 Supported Syntax

**Complete Handlebars-style Sline syntax support:**

```sline
{{! This is a comment }}

{{#if user.isLoggedIn}}
  <h1>Welcome, {{user.name}}!</h1>
{{else}}
  <h1>Please log in</h1>
{{/if}}

{{#for item in products}}
  <div class="product">
    {{item.title | capitalize}}
    {{item.price | money}}
  </div>
{{/for}}

{{#component "button" type="primary"}}
  Click me
{{/component}}
```

### 📋 Syntax Categories

**Block Statements (require closing tags)**
- **Conditionals**: `{{#if}}...{{/if}}`, `{{#unless}}...{{/unless}}`
- **Loops**: `{{#for}}...{{/for}}`, `{{#each}}...{{/each}}`
- **Schema**: `{{#schema}}...{{/schema}}`
- **Content**: `{{#capture}}...{{/capture}}`

**Self-closing Tags**
- **Layout**: `{{#layout "theme" /}}`
- **Variables**: `{{#var name = "value" /}}`
- **Components**: `{{#component "button" text="Click" /}}`
- **Content Areas**: `{{#content "main" /}}`, `{{#sections "header" /}}`
- **Assets**: `{{#stylesheet}}`, `{{#script}}`

**Variables and Comments**
- **Output**: `{{variable}}`, `{{{unescaped}}}`
- **Comments**: `{{! comment }}`, `{{!-- block comment --}}`

### 🤖 Intelligent Autocomplete

Experience powerful autocomplete with **100+ completion items** covering the complete Sline ecosystem:

#### 🔧 Filter Completion (80+ filters)

Triggered automatically after `|` with support for filter chaining:

```sline
{{ product.title | }}               <!-- Shows all 80+ filters -->
{{ product.title | cap }}           <!-- Filters to: capitalize(), etc. -->
{{ product.price | money() | up }}  <!-- Supports filter chains -->
```

**Filter Categories:**
- **Text Processing**: `capitalize()`, `upcase()`, `downcase()`, `truncate()`, `strip_html()`
- **Array Operations**: `map()`, `where()`, `sort()`, `join()`, `first()`, `last()`, `size()`
- **Number Formatting**: `plus()`, `minus()`, `times()`, `divided_by()`, `money()`
- **Date & URL**: `date()`, `asset_url()`, `url_encode()`, `escape()`

#### 🏷️ Tag Completion (20+ tags)

Triggered after `{{#` with intelligent categorization:

```sline
{{#}}                               <!-- Shows all 20+ tags -->
{{#cust}}                          <!-- Filters to customer-related tags -->
{{#image}}                         <!-- Shows image_tag -->
```

**Tag Categories:**
- **Control Flow**: `if`, `for`, `each`, `unless`, `case`, `capture`
- **Layout & Components**: `layout`, `component`, `content`, `section`, `schema`
- **Forms**: `customer_form`, `cart_form`, `contact_form`
- **Media & Assets**: `image_tag`, `stylesheet`, `script`

#### ⚡ Smart Triggers

- **Auto-trigger**: `|` (filters), `#` (tags), `.` (properties)
- **Manual trigger**: `Ctrl+Space` (Windows/Linux) or `Cmd+Space` (Mac)
- **Context-aware**: Only shows relevant suggestions within `{{ }}` expressions
- **Fuzzy matching**: Intelligent partial matching and real-time filtering

#### 💡 Usage Examples

```sline
<!-- Filter chaining -->
{{ product.title | capitalize() | truncate(50) }}
{{ products | where("available", true) | map("title") | join(", ") }}

<!-- Tag usage -->
{{#if product.available}}
  {{#component "product-card" product=product}}
    {{product.title}} - {{product.price | money()}}
  {{/component}}
{{/if}}
```

### 📝 Available Code Snippets

| Prefix | Description |
|--------|-------------|
| `if` | If conditional statement |
| `ifelse` | If-else conditional block |
| `for` | For loop iteration |
| `each` | Each loop iteration |
| `component` | Component call |
| `layout` | Layout template |
| `var` | Variable definition |

## 🛠️ Troubleshooting

### Autocomplete Not Working?

If autocomplete isn't working, try these solutions:

1. **Check file extension** - Ensure file ends with `.sline` or switch language mode manually
2. **Restart language server** - Command Palette → "SLine: Restart SLine Language Server"
3. **Reload window** - `Ctrl+Shift+P` → "Developer: Reload Window"
4. **Check syntax** - Ensure you're inside valid `{{ }}` expressions
5. **Verify settings** - Confirm `slineLanguageServer.enableCompletion` is `true`

### Common Questions

**Q: Why don't I see autocomplete in some locations?**
A: Autocomplete only works inside `{{ }}` expressions. Make sure your cursor is in the right position.

**Q: How do I manually trigger autocomplete?**
A: Press `Ctrl+Space` (Windows/Linux) or `Cmd+Space` (Mac).

**Q: Why aren't filter suggestions showing?**
A: Ensure you're after a pipe `|` character and inside a valid expression. Remember filters use function syntax like `capitalize()`.

### Smart Language Mode Issues

**Q: Extension doesn't detect Sline syntax in HTML files?**
A: Make sure your HTML file contains recognizable Sline patterns like `{{#if}}`, `{{product.title}}`, or `{{price | money}}`.

**Q: How do I disable auto-switch suggestions?**
A: Set `slineLanguageServer.showModeSuggestion` to `false` in VS Code settings.



## 🤝 Contributing

We welcome contributions! Here's how you can help:

1. **Fork** the repository
2. **Create** your feature branch (`git checkout -b feature/AmazingFeature`)
3. **Commit** your changes (`git commit -m 'Add some AmazingFeature'`)
4. **Push** to the branch (`git push origin feature/AmazingFeature`)
5. **Open** a Pull Request

### Development Setup

```bash
# Clone the repository
git clone https://github.com/your-username/sline-highlight-vscode.git

# Install dependencies
npm install

# Compile the extension
npm run compile

# Run tests
npm test

# Package the extension
npm run package
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## � Support & Resources

### Get Help
- 🐛 **Report Issues**: [GitHub Issues](https://github.com/your-username/sline-highlight-vscode/issues)
- � **Discussions**: [GitHub Discussions](https://github.com/your-username/sline-highlight-vscode/discussions)
- 📖 **Documentation**: [Project Wiki](https://github.com/your-username/sline-highlight-vscode/wiki)

### Additional Resources
- 📝 **Changelog**: [CHANGELOG.md](CHANGELOG.md) - Detailed version history
- � **Filter Reference**: [docs/sline_filters_list.md](docs/sline_filters_list.md) - Complete filter documentation
- 🏷️ **Tag Reference**: [docs/sline_tags_list.md](docs/sline_tags_list.md) - Complete tag documentation
- 🚀 **Quick Start Guide**: [docs/QUICK-START.md](docs/QUICK-START.md) - Detailed setup instructions

## 📁 Project Structure

```
sline-highlight-vscode/
├── src/                          # Source code
│   ├── client.ts                 # Language client
│   ├── extension.ts              # Main extension entry
│   ├── server/                   # Language server
│   │   ├── server.ts             # Main server logic
│   │   ├── referenceData.ts      # Tag/filter/object data management
│   │   ├── contextAnalysis.ts    # Context analysis for completion
│   │   ├── snippets.ts           # Code snippets management
│   │   └── *.test.ts             # Unit tests
│   ├── tag.json                  # Sline tags reference data
│   ├── objects.json              # Sline objects reference data
│   └── filter.json               # Sline filters reference data
├── syntaxes/                     # Syntax highlighting
│   └── sline.tmLanguage.json     # TextMate grammar
├── snippets/                     # Code snippets
│   └── sline.json                # Sline code snippets
├── docs/                         # Documentation
│   ├── sline_tags_list.md        # Complete tags reference
│   ├── sline_filters_list.md     # Complete filters reference
│   └── QUICK-START.md            # Quick start guide
├── tempfiles/                    # Temporary test files
├── language-configuration.json   # Language configuration
├── package.json                  # Extension manifest
└── CHANGELOG.md                  # Version history
```

## 🌟 Show Your Support

If this extension helps improve your Sline development experience, please:

- ⭐ **Star** the repository on GitHub
- 📝 **Rate** the extension on the VS Code Marketplace
- 🐛 **Report** any issues you encounter
- 💡 **Suggest** new features or improvements

---

**Happy coding with Sline! 🚀**
