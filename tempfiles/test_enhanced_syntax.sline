{{! Test file for enhanced Sline syntax highlighting }}

{{! Raw values with whitespace control }}
{{{~ product.description ~}}}
{{{ product.title }}}

{{! Block tags with whitespace control }}
{{~# for item in products ~}}
    <div class="product">
        <h2>{{ item.title }}</h2>
        <p>{{ item.description | truncate(100) }}</p>
        
        {{! Conditional blocks }}
        {{# if item.available }}
            <span class="available">In Stock</span>
        {{# else }}
            <span class="unavailable">Out of Stock</span>
        {{/ if }}
        
        {{! Complex expressions with operators }}
        {{# if item.price > 100 && item.discount }}
            <span class="discounted">Special Price!</span>
        {{/ if }}
        
        {{! Filters with arguments }}
        <p>Price: {{ item.price | money(code="USD") }}</p>
        <p>Created: {{ item.created_at | date(format="%B %d, %Y") }}</p>
        
        {{! Object property access }}
        <p>Vendor: {{ item.vendor.name }}</p>
        <p>Category: {{ item.collections[0].title }}</p>
        
        {{! Literals and comparisons }}
        {{# if item.rating >= 4.5 }}
            <span class="high-rating">⭐⭐⭐⭐⭐</span>
        {{/ if }}
        
        {{# if item.tags contains "featured" }}
            <span class="featured">Featured Product</span>
        {{/ if }}
    </div>
{{/ for }}

{{! Self-closing tags }}
{{# payment_type_svg "visa" class="payment-icon" /}}
{{# format_address customer.default_address /}}

{{! Comments with different styles }}
{{!-- This is a block comment --}}
{{! Single line comment }}

{{! Complex filter chains }}
{{ product.tags | join(", ") | upcase() | truncate(50) }}

{{! Boolean and nil literals }}
{{# if product.available == true }}
    Available
{{# else if product.available == false }}
    Unavailable  
{{# else if product.available == nil }}
    Unknown
{{/ if }}

{{! Nested expressions }}
{{ products | where("available", true) | sort("price") | first.title }}

{{! Hash arguments in filters }}
{{ image | image_url(width=300, height=200, quality=90) }}

{{! Array access with variables }}
{{ product.variants[variant_index].price }}
{{ product.options_with_values[0].values[selected_option] }}
