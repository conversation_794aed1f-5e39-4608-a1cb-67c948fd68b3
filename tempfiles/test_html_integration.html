<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ shop.name }} - {{ page.title | default("Home") }}</title>
    
    {{! SEO meta tags }}
    <meta name="description" content="{{ page.description | default(shop.description) | truncate(160) }}">
    <meta property="og:title" content="{{ page.title | default(shop.name) }}">
    <meta property="og:description" content="{{ page.description | default(shop.description) }}">
    
    {{! Conditional CSS loading }}
    {{# if settings.enable_custom_css }}
        <link rel="stylesheet" href="{{ 'custom.css' | asset_url() }}">
    {{/ if }}
    
    {{{ content_for_header }}}
</head>
<body class="{{ template | handle_to_class }}">
    
    {{!-- Header section --}}
    <header class="site-header">
        <div class="container">
            {{# if shop.logo }}
                <a href="{{ routes.root_url }}" class="logo">
                    <img src="{{ shop.logo | image_url(width=200) }}" alt="{{ shop.name }}">
                </a>
            {{# else }}
                <h1><a href="{{ routes.root_url }}">{{ shop.name }}</a></h1>
            {{/ if }}
            
            {{! Navigation menu }}
            <nav class="main-nav">
                <ul>
                    {{# for link in linklists.main-menu.links }}
                        <li class="{{# if link.active }}active{{/ if }}">
                            <a href="{{ link.url }}">{{ link.title }}</a>
                            
                            {{! Dropdown menu }}
                            {{# if link.links.size > 0 }}
                                <ul class="dropdown">
                                    {{# for child_link in link.links }}
                                        <li><a href="{{ child_link.url }}">{{ child_link.title }}</a></li>
                                    {{/ for }}
                                </ul>
                            {{/ if }}
                        </li>
                    {{/ for }}
                </ul>
            </nav>
            
            {{! Cart icon with count }}
            <a href="{{ routes.cart_url }}" class="cart-link">
                Cart ({{ cart.item_count }})
                {{# if cart.item_count > 0 }}
                    <span class="cart-total">{{ cart.total_price | money() }}</span>
                {{/ if }}
            </a>
        </div>
    </header>
    
    {{! Main content area }}
    <main class="main-content">
        {{# if template == "product" }}
            {{! Product page specific content }}
            <div class="product-page">
                <div class="product-images">
                    {{# if product.featured_image }}
                        <img src="{{ product.featured_image | image_url(width=600) }}" alt="{{ product.title }}">
                    {{/ if }}
                    
                    {{! Product image gallery }}
                    {{# if product.images.size > 1 }}
                        <div class="product-gallery">
                            {{# for image in product.images }}
                                <img src="{{ image | image_url(width=100) }}" alt="{{ product.title }}">
                            {{/ for }}
                        </div>
                    {{/ if }}
                </div>
                
                <div class="product-info">
                    <h1>{{ product.title }}</h1>
                    <div class="price">
                        {{# if product.compare_at_price > product.price }}
                            <span class="sale-price">{{ product.price | money() }}</span>
                            <span class="original-price">{{ product.compare_at_price | money() }}</span>
                        {{# else }}
                            <span class="price">{{ product.price | money() }}</span>
                        {{/ if }}
                    </div>
                    
                    {{! Product form }}
                    {{# product_form product }}
                        {{# if product.has_only_default_variant }}
                            <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                        {{# else }}
                            {{! Variant selector }}
                            {{# for option in product.options_with_values }}
                                <div class="option-selector">
                                    <label>{{ option.name }}:</label>
                                    <select name="options[{{ option.name }}]">
                                        {{# for value in option.values }}
                                            <option value="{{ value }}">{{ value }}</option>
                                        {{/ for }}
                                    </select>
                                </div>
                            {{/ for }}
                        {{/ if }}
                        
                        <button type="submit" class="add-to-cart-btn">
                            {{# if product.available }}
                                Add to Cart
                            {{# else }}
                                Sold Out
                            {{/ if }}
                        </button>
                    {{/ product_form }}
                    
                    {{! Product description }}
                    <div class="product-description">
                        {{ product.description }}
                    </div>
                </div>
            </div>
            
        {{# else if template == "collection" }}
            {{! Collection page }}
            <div class="collection-page">
                <h1>{{ collection.title }}</h1>
                {{# if collection.description }}
                    <div class="collection-description">{{ collection.description }}</div>
                {{/ if }}
                
                {{! Product grid }}
                <div class="product-grid">
                    {{# for product in collection.products }}
                        <div class="product-card">
                            <a href="{{ product.url }}">
                                {{# if product.featured_image }}
                                    <img src="{{ product.featured_image | image_url(width=300) }}" alt="{{ product.title }}">
                                {{/ if }}
                                <h3>{{ product.title }}</h3>
                                <p class="price">{{ product.price | money() }}</p>
                            </a>
                        </div>
                    {{/ for }}
                </div>
            </div>
            
        {{# else }}
            {{! Default page content }}
            <div class="page-content">
                {{ content_for_layout }}
            </div>
        {{/ if }}
    </main>
    
    {{! Footer }}
    <footer class="site-footer">
        <div class="container">
            <p>&copy; {{ "now" | date("%Y") }} {{ shop.name }}. All rights reserved.</p>
            
            {{! Payment icons }}
            <div class="payment-icons">
                {{# for type in shop.enabled_payment_types }}
                    {{# payment_type_svg type class="payment-icon" /}}
                {{/ for }}
            </div>
        </div>
    </footer>
    
    {{! JavaScript }}
    <script src="{{ 'theme.js' | asset_url() }}"></script>
    {{{ content_for_footer }}}
</body>
</html>
