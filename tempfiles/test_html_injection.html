<!DOCTYPE html>
<html>
<head>
    <title>测试修复后的 Sline 语法高亮一致性</title>
</head>
<body>
    <!-- 测试同组标签的开始和结束符号着色一致性 -->

    <!-- 1. 简单的变量输出 - 检查 {{ 和 }} 颜色是否一致 -->
    <div class="{{product.class}}" id="{{product.id}}">
        {{product.title}}
        {{customer.name}}
    </div>
    
    <!-- 2. 条件表达式 -->
    <div class="product {{#if product.available}}available{{else}}unavailable{{/if}}">
        Conditional class
    </div>
    
    <!-- 3. 复杂的混合属性 -->
    <a href="{{product.url}}" 
       class="btn {{#if product.featured}}btn-featured{{/if}} {{product.category | slugify}}"
       data-id="{{product.id}}"
       data-price="{{product.price | money}}"
       data-available="{{product.available}}">
        {{product.title}}
    </a>
    
    <!-- 4. 嵌套的条件和字符串比较 -->
    <div class="card {{#if settings.layout == 'grid'}}card-grid{{else}}card-list{{/if}}"
         data-config='{"type": "{{product.type}}", "id": {{product.id}}}'>
        
        <!-- 5. 自闭合标签在属性中 -->
        <img src="{{#placeholder_svg 'product' class='icon' /}}" 
             alt="{{product.title | escape}}"
             class="{{#if product.image}}product-image{{else}}placeholder{{/if}}">
    </div>
    
    <!-- 6. 循环中的属性 -->
    {{#for item in collection.products}}
        <div class="item item-{{item.id}} {{#if item.featured}}featured{{/if}}"
             data-index="{{forloop.index}}"
             data-title="{{item.title | truncate(20)}}">
            {{item.title}}
        </div>
    {{/for}}
    
    <!-- 7. 复杂的嵌套表达式 -->
    <form action="{{routes.cart_add_url}}" 
          method="post"
          class="product-form {{#if product.variants.size > 1}}has-variants{{/if}}">
        
        <select name="id" 
                class="variant-select {{#if product.variants.size == 1}}hidden{{/if}}"
                data-variants='{{product.variants | json}}'>
            {{#for variant in product.variants}}
                <option value="{{variant.id}}" 
                        {{#if variant.available}}{{else}}disabled{{/if}}
                        data-price="{{variant.price | money}}">
                    {{variant.title}} - {{variant.price | money}}
                </option>
            {{/for}}
        </select>
        
        <button type="submit" 
                class="btn btn-primary {{#unless product.available}}disabled{{/unless}}"
                {{#unless product.available}}disabled{{/unless}}>
            {{#if product.available}}
                Add to Cart - {{product.price | money}}
            {{else}}
                Sold Out
            {{/if}}
        </button>
    </form>
    
    <!-- 8. 样式属性中的表达式 -->
    <div style="background-color: {{settings.primary_color}}; 
                width: {{product.image.width}}px; 
                height: {{product.image.height}}px;"
         class="{{#if product.image}}has-image{{else}}no-image{{/if}}">
        Dynamic styling
    </div>
    
    <!-- 9. 事件处理器中的表达式 -->
    <button onclick="addToCart('{{product.id}}', {{product.price}})"
            onmouseover="showTooltip('{{product.description | escape}}')"
            class="{{#if product.available}}available{{else}}unavailable{{/if}}">
        Interactive button
    </button>
    
    <!-- 10. 复杂的 JSON 数据属性 -->
    <div data-product='{"id": "{{product.id}}", 
                        "title": "{{product.title | escape}}", 
                        "price": {{product.price}}, 
                        "available": {{product.available}},
                        "variants": {{product.variants | json}}}'>
        JSON data attribute
    </div>
    
</body>
</html>
