// Basic functionality test for enhanced features
import { ContextAnalyzer } from '../src/server/contextAnalysis';
import { TextDocument } from 'vscode-languageserver-textdocument';

describe('Basic Enhanced Functionality', () => {
    
    function createDocument(content: string): TextDocument {
        return TextDocument.create('test://test.sline', 'sline', 1, content);
    }

    test('should detect tag completion context', () => {
        const doc = createDocument('{{# ');
        const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 4 });
        
        expect(context.type).toBe('tag');
        expect(context.prefix).toBe('');
    });

    test('should detect object property context', () => {
        const doc = createDocument('{{ product.ti');
        const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 13 });
        
        expect(context.type).toBe('object_property');
        expect(context.objectName).toBe('product');
        expect(context.prefix).toBe('ti');
    });

    test('should detect filter context', () => {
        const doc = createDocument('{{ product.title | tr');
        const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 21 });
        
        expect(context.type).toBe('filter');
        expect(context.prefix).toBe('tr');
    });

    test('should handle unknown context gracefully', () => {
        const doc = createDocument('regular text without sline');
        const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 10 });
        
        expect(context.type).toBe('unknown');
    });

    test('should handle incomplete expressions', () => {
        const doc = createDocument('{{ ');
        const context = ContextAnalyzer.analyzeCompletionContext(doc, { line: 0, character: 3 });
        
        expect(context.type).toBe('unknown');
        expect(context.prefix).toBe('');
    });
});
