{{! Test file for enhanced context analysis }}

{{! Test 1: Basic tag completion }}
{{# 

{{! Test 2: Tag with parameters }}
{{# for item in 

{{! Test 3: Object property access }}
{{ product.

{{! Test 4: Nested object property }}
{{ customer.default_address.

{{! Test 5: Filter completion }}
{{ product.title | 

{{! Test 6: Filter with parameters }}
{{ product.price | money(

{{! Test 7: Filter with hash parameters }}
{{ image | image_url(width=300, height=

{{! Test 8: Complex filter chain }}
{{ product.tags | join(", ") | 

{{! Test 9: Raw value }}
{{{ product.description

{{! Test 10: Raw value with whitespace control }}
{{{~ product.title

{{! Test 11: Array access }}
{{ product.variants[0].

{{! Test 12: Complex expression }}
{{ products | where("available", true) | 

{{! Test 13: Conditional with operators }}
{{# if product.price > 100 && product.

{{! Test 14: Self-closing tag }}
{{# payment_type_svg "visa" class=

{{! Test 15: Comment completion }}
{{!-- This is a 

{{! Test 16: Nested expressions }}
{{ product.collections[collection_index].

{{! Test 17: Filter parameter completion }}
{{ date_string | date(format=

{{! Test 18: Boolean literals }}
{{# if product.available == 

{{! Test 19: Multiple filters }}
{{ text | truncate(100) | upcase() | 

{{! Test 20: Hash arguments in tags }}
{{# customer_login_form return_to=
