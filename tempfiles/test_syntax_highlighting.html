<!DOCTYPE html>
<html>
<head>
    <title>测试语法高亮问题</title>
</head>
<body>
    <!-- 测试混合语法的情况 - 这些应该正确高亮 -->

    <!-- 测试 HTML 属性中的 Sline 表达式 -->
    <a href="{{ article.url | default('javascript:void(0)') }}"
       class="block-article-card {{#if settings.article_card_style == "card"}}article-card-border-shadow{{/if}} {{ block.settings | class_list() }}"
       data-test="{{product.id}}"
       {{block.shopline_attributes}}>

    <!-- 测试嵌套的 Sline 块 -->
    {{#blocks}}
        {{#if forblock.type == "article/cover"}}
            <div class="block-article-card__cover {{#if settings.article_card_style != "card"}}article-card-border-shadow{{/if}}">
                {{#block
                    image=article.image
                    mobile_columns=props.mobile_columns
                    mobile_columns=props.desktop_columns
                /}}
            </div>
        {{#else if forblock.type == "article/info" /}}
            {{#block article=article /}}
        {{/if}}
    {{/blocks}}

    <!-- 测试字符串中的引号 -->
    <div class="test" data-config='{"key": "value", "nested": "{{product.title}}"}'>
        {{#if product.available}}
            <span class="available">Available</span>
        {{else}}
            <span class="unavailable">Sold Out</span>
        {{/if}}
    </div>

    <!-- 测试 placeholder_svg 标签 -->
    <div class="placeholder-container">
        {{#placeholder_svg "product" class="placeholder-icon" /}}
    </div>

    <span class="body2 body-font-bold button button--link block-article-card__button hidden-mobile">
        READ NOW
    </span>
</body>
</html>
