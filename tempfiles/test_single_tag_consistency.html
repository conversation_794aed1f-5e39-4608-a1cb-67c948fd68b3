<!DOCTYPE html>
<html>
<head>
    <title>测试单个标签内标点符号一致性</title>
</head>
<body>
    <!-- 测试单个 if 标签的开始和结束标点符号颜色一致性 -->
    
    <!-- 1. 简单的 if 标签 - {{# 和 }} 应该是相同颜色 -->
    {{#if product.available}}
        <span>Available</span>
    {{/if}}
    
    <!-- 2. 带条件的 if 标签 - 所有标点符号应该一致 -->
    {{#if settings.article_card_style == "card"}}
        <div class="card-style">Card Layout</div>
    {{/if}}
    
    <!-- 3. for 循环标签 - 开始和结束标点符号应该一致 -->
    {{#for item in collection.products}}
        <div>{{item.title}}</div>
    {{/for}}
    
    <!-- 4. 嵌套标签 - 所有层级的标点符号都应该一致 -->
    {{#if customer.logged_in}}
        {{#if customer.orders.size > 0}}
            <p>You have {{customer.orders.size}} orders</p>
        {{else}}
            <p>No orders yet</p>
        {{/if}}
    {{else}}
        <p>Please log in</p>
    {{/if}}
    
    <!-- 5. 自闭合标签 - 开始和结束标点符号应该一致 -->
    {{#placeholder_svg "product" class="icon" /}}
    {{#layout "theme" /}}
    {{#var price = product.price /}}
    
    <!-- 6. 变量输出 - 开始和结束标点符号应该一致 -->
    {{product.title}}
    {{product.price}}
    {{customer.name}}
    
    <!-- 7. 带过滤器的变量 - 所有标点符号应该一致 -->
    {{product.price | money}}
    {{product.image | image_url('400x400')}}
    {{product.title | truncate(50)}}
    
    <!-- 8. HTML 属性中的表达式 - 标点符号应该一致 -->
    <div class="product {{#if product.featured}}featured{{/if}}"
         data-id="{{product.id}}"
         data-available="{{product.available}}">
        
        <!-- 9. 复杂的嵌套属性表达式 -->
        <a href="{{product.url}}" 
           class="btn {{#if product.available}}btn-primary{{else}}btn-disabled{{/if}}"
           data-config='{"id": "{{product.id}}", "price": "{{product.price}}"}'>
            {{#if product.available}}
                Add to Cart - {{product.price | money}}
            {{else}}
                Sold Out
            {{/if}}
        </a>
    </div>
    
    <!-- 10. else if 标签 - 所有标点符号应该一致 -->
    {{#if product.type == "simple"}}
        <div>Simple Product</div>
    {{#else if product.type == "variable"}}
        <div>Variable Product</div>
    {{else}}
        <div>Other Product Type</div>
    {{/if}}
    
    <!-- 11. 注释标签 - 标点符号应该一致 -->
    {{!-- 这是一个多行注释
          应该正确高亮 --}}
    {{! 这是单行注释 }}
    
    <!-- 12. 三重大括号 - 标点符号应该一致 -->
    <div class="raw-content">{{{product.description}}}</div>
    
</body>
</html>
