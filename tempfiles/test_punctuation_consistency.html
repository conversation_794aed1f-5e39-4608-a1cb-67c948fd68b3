<!DOCTYPE html>
<html>
<head>
    <title>测试标点符号一致性</title>
</head>
<body>
    <!-- 测试所有类型的 Sline 标点符号应该有一致的颜色 -->
    
    <!-- 1. 块级标签开始 - {{# -->
    {{#if product.available}}
        <div class="available">Available</div>
    {{/if}}
    
    <!-- 2. 块级标签结束 - {{/ -->
    {{#for item in collection.products}}
        <span>{{item.title}}</span>
    {{/for}}
    
    <!-- 3. else 标签 - {{else}} -->
    {{#if customer.logged_in}}
        <p>Welcome back!</p>
    {{else}}
        <p>Please log in</p>
    {{/if}}
    
    <!-- 4. 变量输出 - {{ }} -->
    <h1>{{product.title}}</h1>
    <p>{{product.description}}</p>
    
    <!-- 5. 自闭合标签 - {{#tag /}} -->
    {{#placeholder_svg "product" class="icon" /}}
    {{#layout "theme" /}}
    {{#var price = product.price /}}
    
    <!-- 6. HTML 属性中的 Sline 表达式 -->
    <div class="product {{#if product.featured}}featured{{/if}}"
         data-id="{{product.id}}"
         data-price="{{product.price | money}}">
        
        <!-- 7. 复杂的嵌套表达式 -->
        <a href="{{product.url}}" 
           class="btn {{#if product.available}}btn-primary{{else}}btn-disabled{{/if}}">
            {{#if product.available}}
                Add to Cart
            {{else}}
                Sold Out
            {{/if}}
        </a>
    </div>
    
    <!-- 8. 带过滤器的表达式 -->
    <span class="price">{{product.price | money}}</span>
    <img src="{{product.image | image_url('400x400')}}" alt="{{product.title}}">
    
    <!-- 9. 条件表达式中的字符串比较 -->
    {{#if settings.layout == "grid"}}
        <div class="grid-layout">Grid View</div>
    {{/if}}
    
    <!-- 10. 嵌套的块级标签 -->
    {{#blocks}}
        {{#if forblock.type == "text"}}
            <div class="text-block">
                {{#block content=forblock.content /}}
            </div>
        {{#else if forblock.type == "image"}}
            <div class="image-block">
                {{#block image=forblock.image /}}
            </div>
        {{/if}}
    {{/blocks}}
    
    <!-- 11. 注释 -->
    {{!-- 这是一个 Sline 注释 --}}
    {{! 这是另一种注释格式 }}
    
    <!-- 12. 三重大括号（不转义输出） -->
    <div class="raw-html">{{{product.description}}}</div>
    
</body>
</html>
