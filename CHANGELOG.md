# 更新日志

本文件记录了 SLine Highlight VSCode Extension 的所有重要更改。

## [0.1.4] - 2024-12-19

### 🔧 Configuration Changes
- ✅ **暂时禁用对象属性验证**: 由于对象属性数据还不够完善，暂时禁用了 "Unknown property" 语法提示，避免误报
- 🔍 **调试过滤器验证问题**: 调查并验证了 `plus` 过滤器的识别问题，确认过滤器数据加载正常
- 🔧 **重构验证系统**: 统一了标签、过滤器和对象的验证逻辑，移除了重复的验证系统，降低了系统复杂度
  - 移除了 `findBasicSyntaxErrors` 中的重复验证逻辑
  - 统一使用 `referenceDataManager` 进行所有验证
  - 移除了硬编码的 `SLINE_TAGS` 和 `SLINE_FILTERS` 常量
  - 简化了自动完成和错误检测的代码路径
  - 避免了竞争条件和不一致的验证结果
- 🔧 **修复智能语言模式切换**: 移除了 package.json 中 `.html` 扩展名的自动关联，恢复智能检测功能
  - 问题原因：`.html` 文件被自动识别为 `sline` 语言，导致智能检测逻辑不被触发
  - 解决方案：只保留 `.sline` 扩展名的自动关联，让 `.html` 文件保持 `html` 语言模式
  - 现在：打开包含 SLine 语法的 `.html` 文件时会正确触发智能模式切换建议
- 🔧 **修复标签验证缺失**: 重新添加了 `supplementMissingTags` 和 `supplementMissingFilters` 方法
  - 问题原因：重构过程中这些方法意外丢失，导致某些标签（如 `set`）无法被识别
  - 解决方案：重新实现了标签和过滤器的补充逻辑，确保所有常用标签和过滤器都可用
  - 现在：`set`、`reset_customer_password_form` 等标签都能被正确识别
- 🎯 **优化自动完成用户体验**: 改进了 `{{` 自动完成后的上下文分析
  - 问题：用户输入 `{{` 自动完成为 `{{}}`，然后输入 `#` 时会产生错误的语法建议
  - 解决方案：增强上下文分析器，能够智能识别自动完成的大括号场景
  - 现在：在自动完成的 `{{}}` 中输入 `#` 或标签名时，提供正确的标签建议
  - 修复了对象属性访问的正则表达式匹配问题

## [0.2.0] - 2025-07-30

### 🚀 Major Optimizations Based on Official Sline Specification

#### 🔧 Critical Bug Fixes
- ✅ **修复括号自动完成问题**: 移除了导致 `{{#` 自动完成为 `{{#{{/` 的错误配置
  - 问题原因：`language-configuration.json` 中的 `["{{#", "{{/"]` 自动闭合对配置
  - 解决方案：移除该配置，改为在语言服务器中实现智能标签完成
  - 现在：输入 `{{#` 后会智能提示标签名称，并自动生成正确的闭合标签

#### 🎨 Enhanced Syntax Highlighting
- ✅ **对齐官方 OHM 语法规范**: 基于 `docs/sline-html-parser/grammar/sline-html.ohm` 更新语法高亮
  - 新增支持原始值语法：`{{{ content }}}` 和 `{{{~ content ~}}}`
  - 新增支持空白控制语法：`{{~` 和 `~}}`
  - 新增支持自闭合标签：`{{# tag_name /}}`
  - 改进复杂表达式解析：逻辑运算符、比较运算符、过滤器链
  - 新增支持数组访问：`array[index]` 和对象属性：`object.property`
  - 改进注释语法：`{{! comment }}` 和 `{{!-- block comment --}}`

#### 🧠 Intelligent Language Server
- ✅ **增强上下文分析**: 基于官方语法规范改进代码补全和智能感知
  - 智能标签完成：自动检测块标签并生成闭合标签
  - 改进对象属性补全：支持嵌套属性和数组访问
  - 增强过滤器补全：支持哈希参数和过滤器链
  - 新增标签参数补全：为标签提供语法提示
  - 支持空白控制语法的上下文分析

#### 🌐 HTML Integration Improvements
- ✅ **优化 HTML 文件集成**: 改进注入语法以更好地支持 HTML 中的 Sline 模板
  - 扩展注入选择器：支持更多 HTML 上下文
  - 新增注释语法支持：在 HTML 中正确高亮 Sline 注释
  - 改进原始值语法：在 HTML 属性和内容中正确解析
  - 优化性能：减少不必要的语法解析

#### 🧪 Comprehensive Testing
- ✅ **新增全面测试套件**: 为所有增强功能创建了详细的单元测试
  - 上下文分析测试：验证各种 Sline 语法的正确识别
  - 引用数据管理测试：确保标签、过滤器、对象数据的正确加载和使用
  - 集成测试：验证语言服务器的端到端功能
  - 性能测试：确保大文档的快速响应
  - HTML 集成测试：验证在 HTML 文件中的正确工作

#### 📁 Architecture Improvements
- ✅ **代码结构优化**: 改进项目架构以提高可维护性和扩展性
  - 模块化语法解析：更好的关注点分离
  - 增强错误处理：优雅处理边缘情况
  - 改进性能：优化补全和验证算法
  - 更好的类型安全：增强 TypeScript 类型定义

### 📊 Technical Improvements
- 🔧 **基于官方规范的语法解析**: 使用 `sline-html-parser` 和 `sline-fmt` 包的语法定义
- 🚀 **性能优化**: 改进大文档的处理速度和内存使用
- 🛡️ **错误处理**: 增强对格式错误和边缘情况的处理
- 📝 **代码质量**: 改进代码组织和文档

### 🎯 User Experience Enhancements
- ✨ **智能自动完成**: 基于上下文提供更准确的建议
- 🎨 **改进语法高亮**: 支持所有官方 Sline 语法特性
- 🔍 **更好的错误检测**: 基于官方语法规范的验证
- 📱 **无缝 HTML 集成**: 在 HTML 文件中自动工作，无需手动切换语言模式

## [Unreleased]

### 🎨 彻底修复混合语法高亮问题

#### 修复的关键问题
- ✅ **实现注入语法（Injection Grammar）**: 采用 VS Code 官方推荐的注入语法技术，彻底解决 HTML 属性中的 Sline 表达式高亮问题
- ✅ **彻底统一标点符号着色**: 解决了单个 Sline 标签内 `{{#` 和 `}}` 颜色不一致的问题
- ✅ **标准化作用域命名**: 将所有 Sline 标点符号统一使用 `punctuation.definition.tag.sline` 作用域
- ✅ **修复同标签内标点符号不一致**: 确保 `{{#if condition}}` 中的开始和结束标点符号使用相同颜色
- ✅ **完美支持混合语法**: HTML 属性中的 Sline 表达式现在能够正确识别和高亮
- ✅ **嵌入式语言支持**: 使用 `meta.embedded.inline.sline` 作用域确保 VS Code 正确处理嵌入的 Sline 语法
- ✅ **优化字符串匹配规则**: 重新设计了字符串匹配逻辑，区分 HTML 字符串和 Sline 表达式内的字符串

#### 技术改进
- 🚀 **注入语法技术**: 实现了 VS Code 官方推荐的注入语法（Injection Grammar）技术
- 🎯 **精确的注入选择器**: 使用 `L:string.quoted.double.html, L:string.quoted.single.html` 精确定位 HTML 属性字符串
- 🔗 **嵌入式语言映射**: 通过 `embeddedLanguages` 配置将 `meta.embedded.inline.sline` 映射到 `sline` 语言
- 🎨 **完全统一的作用域**: 采用单一的 `punctuation.definition.tag.sline` 作用域命名所有标点符号
- 🔧 **彻底消除作用域不一致**: 移除了所有不一致的作用域（`support.constant.sline`、`punctuation.handlebars`、`punctuation.definition.tag.begin/end.sline`）
- 🔍 **专用注入语法文件**: 创建了独立的 `sline-injection.tmLanguage.json` 文件处理混合语法
- 📐 **采用 Handlebars 成功模式**: 参考 Handlebars 语法高亮插件，使用 `\\{\\{` 和 `\\}\\}` 作为完整的捕获组
- 🎯 **改进正则表达式**: 将 `{{` 和 `}}` 作为整体捕获，避免分割导致的着色不一致
- 🏗️ **重构语法规则结构**: 分离了 HTML 字符串和 Sline 字符串的处理逻辑
- 🌈 **跨主题兼容性**: 确保在不同 VS Code 主题下都能保持一致的视觉效果
- ⚡ **简化作用域管理**: 使用单一作用域减少了主题开发者的配置复杂度

#### 解决的具体场景
- **HTML 属性中的完美支持**:
  - 简单变量: `class="{{product.class}}" id="{{product.id}}"`
  - 条件表达式: `class="product {{#if product.available}}available{{else}}unavailable{{/if}}"`
  - 复杂混合: `class="btn {{#if product.featured}}btn-featured{{/if}} {{product.category | slugify}}"`
  - JSON 数据属性: `data-config='{"type": "{{product.type}}", "id": {{product.id}}}'`
- **单标签内标点符号一致性**: `{{#if condition}}` 中的 `{{#` 和 `}}` 现在使用完全相同的颜色
- **所有标签类型的一致性**:
  - 块级标签: `{{#if}}...{{/if}}`、`{{#for}}...{{/for}}`
  - 变量输出: `{{product.title}}`、`{{customer.name}}`
  - 自闭合标签: `{{#placeholder_svg "product" /}}`
  - else 标签: `{{else}}`、`{{#else if condition}}`
- **嵌套表达式**: `{{#if settings.article_card_style == "card"}}article-card-border-shadow{{/if}}`
- **字符串中的嵌套引号**: `data-config='{"key": "{{product.title}}"}'`
- **复杂的混合语法**: `href="{{ article.url | default('javascript:void(0)') }}"`
- **条件表达式中的字符串比较**: `{{#if settings.style == "card"}}`
- **带过滤器的表达式**: `{{product.price | money}}`、`{{product.image | image_url('400x400')}}`
- **事件处理器中的表达式**: `onclick="addToCart('{{product.id}}', {{product.price}})"`
- **样式属性中的表达式**: `style="background-color: {{settings.primary_color}};"`

#### 问题根因分析
- **混合语法处理缺失**: 原有语法没有专门处理 HTML 属性中的 Sline 表达式
- **正则表达式分割问题**: 将 `{{` 和 `}}` 分割成多个捕获组导致渲染引擎处理不一致
  - 原有模式: `({{)(~?)` 将开始标记分成两部分
  - 原有模式: `(/~?)(~?}})` 将结束标记分成两部分
- **核心问题**: 同一个标签内的开始和结束标点符号使用了不同的作用域
  - `beginCaptures` 使用 `punctuation.definition.tag.begin.sline`
  - `endCaptures` 使用 `punctuation.definition.tag.end.sline`
- **作用域混乱**: 不同的语法规则使用了不同的作用域名称（`support.constant.sline`、`punctuation.handlebars`、`keyword.control.sline`）
- **字符串匹配冲突**: 原有的字符串匹配规则过于宽泛，会匹配任何双引号，包括 HTML 属性中的引号
- **影响范围**:
  - HTML 属性中的 Sline 表达式完全无法正确高亮
  - 单个 Sline 标签内的标点符号颜色不一致
  - 不同类型标签之间的标点符号颜色不一致
  - 不同 VS Code 主题下表现不一致
- **解决方案**:
  - 实现注入语法技术，专门处理 HTML 属性中的 Sline 表达式
  - 采用 Handlebars 成功模式：使用 `\\{\\{` 和 `\\}\\}` 作为完整的捕获组
  - 统一所有 Sline 标点符号使用单一的 `punctuation.definition.tag.sline` 作用域
  - 创建上下文感知的字符串匹配规则
  - 使用 `meta.embedded.inline.sline` 确保正确的语言识别
  - 确保跨主题的一致性和简化主题配置

### 🔧 修复 placeholder_svg 标签识别问题

#### 修复的关键问题
- ✅ **修复 placeholder_svg 标签未知错误**: 解决了 `placeholder_svg` 标签被错误标记为 "Unknown tag" 的问题
- ✅ **补充缺失的标签定义**: 在 `supplementMissingTags()` 方法中添加了所有在 `docs/sline_tags_list.md` 中列出但缺失的标签
- ✅ **完善标签数据库**: 确保所有 56 个官方 Sline 标签都能被正确识别

#### 新增支持的标签
- `placeholder_svg` - 生成占位符 SVG 图标
- `payment_type_svg` - 生成支付类型 SVG 图标
- `company_account_application_form` - 企业账户申请表单
- `contact_form` - 联系表单
- `customer_form` - 客户表单
- `customer_login_link` - 客户登录链接
- `customer_logout_link` - 客户登出链接
- `customer_register_link` - 客户注册链接
- `customer_subscribe_form` - 客户订阅表单
- `customer_unsubscribe_form` - 客户取消订阅表单
- `delete_customer_form` - 删除客户表单
- `format_address` - 地址格式化
- `link_to_customer_login` - 客户登录链接生成
- `link_to_customer_logout` - 客户登出链接生成
- `link_to_customer_register` - 客户注册链接生成
- `localization_form` - 本地化表单
- `new_comment_form` - 新评论表单
- `order_tracking_form` - 订单跟踪表单
- `storefront_password_form` - 店铺密码表单
- `style` - 样式标签
- `switch` - 开关控制标签
- `update_customer_form` - 更新客户表单

#### 技术改进
- 🔍 **基于官方文档**: 根据 `docs/sline_tags_list.md` 中的完整标签列表更新了 `supplementMissingTags()` 方法
- 🛡️ **回退机制增强**: 即使 JSON 文件加载失败，所有官方标签都能通过补充机制被识别
- 🧪 **完整测试覆盖**: 添加了专门的测试文件验证所有新增标签的识别功能
- 📝 **代码注释改进**: 在 `supplementMissingTags()` 方法中添加了基于官方文档的注释说明

#### 问题根因分析
- **问题原因**: `supplementMissingTags()` 方法中的 `allTags` 数组不完整，缺少了 22 个官方标签
- **影响范围**: 当 JSON 文件加载失败或标签定义不完整时，这些标签会被误报为 "Unknown tag"
- **解决方案**: 基于 `docs/sline_tags_list.md` 的官方标签列表，补充了所有缺失的标签定义

## [Unreleased] - 2025-07-15

### 🔧 紧急修复 - 恢复基本功能

#### 修复的关键问题
- ✅ **修复 "Unknown tag" 错误**: 解决了所有有效标签显示为 "Unknown tag" 的问题
- ✅ **恢复自动完成功能**: 修复了自动完成提示列表失效的问题
- ✅ **改进文件路径解析**: 增强了 JSON 参考数据文件的查找逻辑
- ✅ **添加回退机制**: 当 JSON 文件无法加载时，自动使用内置的基本标签和过滤器数据
- ✅ **增强错误处理**: 改进了语言服务器的错误处理和降级机制
- ✅ **修复 else/elseif 标签识别**: 解决了 `else` 和 `elseif` 标签被误报为 "unknown" 的问题（已重新修复）
- ✅ **修复动态对象属性验证**: 解决了 `props`、`settings`、`block` 等动态对象的属性被误报为 "Unknown property" 的问题
- ✅ **修复自动完成功能**: 解决了输入 `{{#` 时不显示标签列表的问题，补充了 JSON 文件中缺失的标签
- ✅ **智能语言模式切换**: 新增 HTML 文件中 SLine 语法的智能检测和自动切换功能
- ✅ **修复逻辑或运算符误识别**: 解决了 `||` 被错误识别为管道符 `|` 导致的 "Unknown filter" 误报问题
  - 问题原因：上下文分析和诊断功能使用简单的字符串匹配，无法区分逻辑或和管道符
  - 解决方案：实现了智能管道符检测算法，能够正确区分 `||` (逻辑或) 和 `|` (管道符)
  - 现在：`props.desktop_size || props.mobile_size` 不再误报 "Unknown filter 'props'" 错误

#### 新增功能
- ✨ **智能语法检测**: 自动检测 HTML 文件中的 SLine 语法模式
  - 🔍 检测标签语法：`{{#if}}`、`{{#for}}`、`{{#component}}` 等
  - 🔍 检测过滤器语法：`{{product.price | money()}}`
  - 🔍 检测对象属性：`{{product.title}}`、`{{customer.name}}` 等
  - 🔍 检测 SLine 注释：`{{!-- comment --}}`
- 🎯 **智能模式建议**: 检测到 SLine 语法时提示用户切换语言模式
- ⚡ **自动切换选项**: 可配置自动切换模式，无需手动操作
- 🛠️ **灵活配置**: 支持关闭提示或启用自动切换
- 📁 **扩展文件支持**: 插件现在同时支持 `.sline` 和 `.html` 文件扩展名

#### 技术改进
- 🔍 **多路径查找**: JSON 文件加载现在尝试多个可能的路径位置
- 🛡️ **优雅降级**: 当高级功能不可用时，自动回退到基本功能
- 📝 **更好的日志**: 增加了详细的调试信息，便于问题诊断
- ⚡ **快速恢复**: 确保扩展在任何情况下都能提供基本的语法支持

### 🚀 重大功能更新 - 智能语言服务器增强

### 新增 JSON 参考数据集成
- ✨ **完整的 SLine 语法数据库**: 集成了 tag.json、objects.json、filter.json 三个参考数据文件
- 📚 **20+ 标签支持**: 包含完整的标签定义、语法、参数和示例
- 🏷️ **15+ 对象支持**: 支持 product、cart、customer、shop 等核心对象
- 🔧 **80+ 过滤器支持**: 涵盖 money、date、image_url 等所有常用过滤器
- 🌐 **双语文档**: 支持中英文描述和文档

### 智能代码完成系统
- 🎯 **上下文感知完成**: 根据光标位置智能识别标签、过滤器、对象属性上下文
- 💡 **参数智能提示**: 为标签和过滤器提供参数名称和类型提示
- 🔍 **模糊搜索支持**: 支持前缀匹配和智能过滤
- 📝 **丰富的元数据**: 每个完成项包含详细描述、语法示例和文档链接
- ⚡ **性能优化**: 预计算完成项，提供毫秒级响应速度

### 悬停文档系统
- 📖 **即时文档**: 鼠标悬停显示完整的语法文档和示例
- 🔗 **官方文档链接**: 直接链接到官方文档页面
- ⚠️ **弃用警告**: 自动标识和警告已弃用的标签和过滤器
- 🎨 **Markdown 格式**: 美观的格式化文档显示
- 🌍 **多语言支持**: 中英文文档同时显示

### 参数签名帮助
- 📋 **实时参数提示**: 输入标签或过滤器时显示参数签名
- 🎯 **活动参数高亮**: 智能识别当前输入的参数位置
- 📚 **参数文档**: 每个参数包含类型信息和详细说明
- ⌨️ **触发字符**: 在 `(`, `,`, ` ` 时自动触发参数提示

### 智能错误诊断
- 🔍 **未知标签检测**: 自动识别拼写错误或不存在的标签
- 🎯 **智能建议**: 为错误的标签和过滤器提供相似名称建议
- 🏷️ **属性验证**: 验证对象属性是否存在，提供可用属性建议
- ⚠️ **弃用检测**: 自动检测并警告使用已弃用的语法
- 💡 **修复建议**: 提供具体的修复建议和替代方案

### 代码片段系统
- 📝 **15+ 预定义片段**: 涵盖控制流、表单、布局、数据展示等常用模式
- 🎨 **分类管理**: 按功能分类（control、form、layout、data、utility）
- 🔧 **参数占位符**: 使用 VSCode 片段格式，支持 Tab 键跳转
- 📚 **完整模板**: 包含登录表单、产品卡片、分页导航等完整模板
- 🌟 **智能插入**: 根据上下文智能插入相关代码片段

### 技术架构升级
- 🏗️ **模块化设计**: ReferenceDataManager、ContextAnalyzer、SnippetManager 独立模块
- ⚡ **异步加载**: 启动时异步加载参考数据，不阻塞编辑器
- 🔄 **实时索引**: 构建高效的查找索引，支持 O(1) 查找性能
- 🧪 **完整测试**: 包含单元测试、集成测试和性能测试
- 📊 **错误处理**: 优雅的错误处理和降级机制

### 开发体验改进
- 🎯 **精确上下文**: 支持嵌套表达式、多行表达式的精确解析
- 🔧 **调试支持**: 详细的日志记录和调试信息
- 📈 **性能监控**: 内置性能监控，确保响应速度
- 🛡️ **向后兼容**: 保持与现有功能的完全兼容
- 🌐 **国际化**: 支持中英文界面和文档

## [0.1.3] - 2025-07-10

### 🎨 语法高亮优化
- ✅ **注释颜色统一**: SLine 注释 `{{!-- --}}` 现在与 HTML 注释保持一致的绿色显示
- ✅ **视觉体验改进**: 提高了 SLine 模板文件的可读性和一致性
- ✅ **块标签颜色统一**: 修复 `{{#` 前三个字符在不同类型块中颜色不一致的问题
  - **问题原因**: 自闭合标签和一般块标签的捕获组处理方式不同导致颜色差异
  - **解决方案**: 统一所有块类型的 `{{#` 标点符号处理，使用相同的捕获组结构
  - **修复范围**: `component`, `content`, `if`, `each`, `else` 等所有块标签

### 🐛 语法检测修复
- ✅ **自闭合标签识别**: 正确识别 SLine 自闭合标签，解决 `{{#layout}}` 和 `{{#var}}` 的误报问题
- ✅ **基于实际代码分析**: 通过分析 theme-sline 项目的 302+ HTML 文件，准确识别自闭合标签模式
- ✅ **支持的自闭合标签**: `layout`, `var`, `component`, `content`, `section`, `sections`, `stylesheet`, `script`, `meta-tags`
- ✅ **智能语法检测**: 区分需要闭合的块级标签（`if`, `each`, `schema`, `capture`）和自闭合标签

### 🔧 LSP 自动完成功能修复
- ✅ **过滤器自动完成修复**: 解决输入 `|` 管道符时显示错误建议的问题
  - **问题原因**: 上下文检查顺序冲突，块助手检查会错误匹配包含 `|` 的情况
  - **解决方案**: 修改块助手正则表达式 `/\{\{#([^}]*)$/` → `/\{\{#([^}|]*)$/`，排除包含管道符的情况
  - **现在正确**: 输入 `{{#component props |` 显示过滤器函数列表而非 SLine 语法
- ✅ **过滤器检测优化**: 改进过滤器上下文识别正则表达式 `/\{\{[^}]*\|\s*([^}]*)$/`
- ✅ **支持的过滤器**: 25+ 过滤器函数包括 `money`, `date`, `upcase`, `asset_url`, `class_list` 等
- ✅ **智能上下文**: 根据输入位置准确区分块助手语法和过滤器语法

### 🆕 新增功能
- ✅ **代码片段支持**: 添加了常用 SLine 语法的代码片段
- ✅ **增强语言配置**: 改进了 SLine 特定语法的支持
- ✅ **ESLint 配置**: 添加了代码质量检查工具
- ✅ **测试框架**: 建立了完整的测试环境
- ✅ **现代化依赖**: 更新到最新版本的依赖包

### 🔧 改进优化
- ✅ **TypeScript 配置**: 使用现代 ES2022 和 Node16 模块
- ✅ **包配置优化**: 完善了 package.json 元数据
- ✅ **更好的 .gitignore**: 改进了忽略文件模式
- ✅ **激活事件**: 优化了扩展激活条件

### 🐛 修复问题
- ✅ **语言服务器激活**: 修复了激活事件配置
- ✅ **括号匹配**: 改进了 SLine 语法的括号匹配
- ✅ **LSP协议兼容性**: 修复 "textDocument/diagnostic" 未处理方法错误
- ✅ **语法高亮颜色完全统一**: 使用统一的`punctuation.handlebars`作用域，彻底解决标点符号颜色不一致问题

## [0.1.3] - 2025-07-09

### 🔧 修复自动完成显示问题

### 修复的关键问题
- ✅ **修复只显示4个提示项的问题**: 重构了完成项逻辑
- ✅ **优化触发条件**: 简化了 `{{#` 的触发逻辑
- ✅ **清理代码结构**: 移除了有问题的大数组定义
- ✅ **添加调试日志**: 便于问题诊断和调试

### 技术改进
- 🔍 **简化触发逻辑**: 移除了过于严格的条件判断
- 📝 **重构完成项数组**: 使用更清晰的数组结构
- 🐛 **修复数组语法**: 解决了导致只显示部分项目的问题
- 📊 **增强日志记录**: 添加详细的调试信息

### 现在支持的完成项 (17个)
- **控制流**: `if`, `each`, `for`, `with`, `unless`, `else`
- **SLine 指令**: `component`, `layout`, `content`, `section`, `sections`
- **区块系统**: `blocks`, `block`
- **变量操作**: `var`, `set`, `capture`
- **配置**: `schema`

## [0.1.2] - 2025-07-09

### 🚀 LSP 功能大幅扩展和优化

### 新增完整的 SLine 语法支持
- ✨ **大幅扩展关键词库**: 基于 theme-sline 项目分析，新增 30+ 关键词
- 🎨 **优化图标显示**: 为不同类型的完成项配置专门的图标和描述
- 📚 **完整的语法覆盖**: 支持所有 SLine 模板引擎语法特性

### 控制流指令 (7个)
- `if/else` - 条件语句 🔀
- `each` - 循环遍历 🔄
- `for` - for 循环 🔄
- `with` - 上下文切换 📦
- `unless` - 反向条件 🚫
- `else` - 分支语句 ↔️

### SLine 特有指令 (7个)
- `component` - 组件调用 🧩
- `layout` - 布局设置 🏗️
- `content` - 内容区域 📄
- `section` - 单个区域 📋
- `sections` - 区域组 📚
- `blocks` - 区块遍历 🧱
- `block` - 区块渲染 🧱

### 变量和数据操作 (4个)
- `var` - 变量定义 📝
- `set` - 变量设置 ✏️
- `capture` - 内容捕获 📥
- `schema` - 配置模式 ⚙️

### 过滤器函数 (25个)
- **货币**: `money`, `money_with_currency`, `money_without_currency`
- **文本**: `upper`, `lower`, `capitalize`, `truncate`, `trim`, `replace`, `split`
- **数组**: `first`, `last`, `size`, `join`
- **日期**: `date`, `format`
- **URL**: `asset_url`, `append`, `prepend`
- **HTML**: `strip_html`, `escape`
- **通用**: `default`, `t`, `json`, `class_list`
- **SLine**: `get_variants`

### 变量提示 (20个)
- **全局对象**: `shop`, `customer`, `request`, `settings`, `routes`, `localization`
- **模板上下文**: `block`, `section`, `props`, `forblock`
- **产品相关**: `product`, `products`, `collection`, `collections`, `variant`
- **内容相关**: `article`, `articles`, `blog`, `blogs`, `page`, `pages`
- **购物相关**: `cart`, `order`
- **循环变量**: `this`, `forloop`

### 用户体验改进
- 🎯 **智能图标**: 不同类型使用不同的 VSCode 图标
- 📖 **丰富描述**: 每个项目都有详细的说明和用法示例
- 🏷️ **分类标签**: 通过 detail 字段显示功能分类
- 🌟 **表情符号**: 使用表情符号增强视觉识别

## [0.1.1] - 2025-07-09

### 🔧 LSP 功能修复和改进

### 修复的问题
- ✅ **智能代码完成现在正常工作**: 修复了上下文感知的自动完成
- ✅ **错误检查功能**: 修复了语法诊断和错误提示
- ✅ **悬停信息**: 修复了基于位置的悬停帮助
- ✅ **文档选择器**: 改进了文件类型识别

### 技术改进
- 🔍 **智能触发**: 根据输入上下文提供相关的完成项
- 📝 **代码片段**: 使用 VSCode 代码片段格式提供更好的插入体验
- 🐛 **调试支持**: 添加详细的日志记录和调试配置
- 📋 **故障排除**: 创建完整的故障排除指南

### 新增功能
- **上下文感知完成**:
  - 在 `{{#` 后提示控制结构
  - 在 `{{#component "` 后提示组件名称
  - 在 `|` 后提示过滤器
- **改进的错误检查**: 更准确的语法验证
- **增强的悬停信息**: 基于光标位置的智能帮助

### 调试和测试
- 📊 **调试配置**: 添加语言服务器调试支持和 VSCode 任务
- 🧪 **测试文件**: 改进的 LSP 功能测试文件
- 📖 **故障排除指南**: 详细的问题诊断和解决方案
- 🚀 **快速启动指南**: 一步步的功能测试指南

### 开发体验改进
- ✅ **修复 F5 启动问题**: 添加正确的 VSCode 任务配置
- 🔧 **编译任务**: 统一的编译和构建流程
- 📝 **文档完善**: 详细的使用和调试文档

## [0.1.0] - 2025-07-09

### 🚀 重大功能更新 - 添加语言服务器协议(LSP)支持

### 新增 LSP 功能
- ✨ **智能代码完成**: 支持 SLine 语法的自动完成
  - Handlebars 控制结构 (`if`, `each`, `with`)
  - SLine 组件调用 (`component`, `layout`, `content`)
  - 过滤器函数 (`money`, `asset_url`, `date`)
- 🔍 **实时错误检查**: 语法诊断和错误提示
  - 未闭合的 Handlebars 标签检测
  - 未定义组件警告
  - 语法错误高亮
- 💡 **悬停信息**: 鼠标悬停显示语法帮助
- 🎯 **触发字符**: 在输入 `{`, `#`, `|`, `.` 时自动触发完成

### 技术架构
- **语言服务器**: 独立的 Node.js 进程处理语言功能
- **客户端集成**: VSCode 插件通过 JSON-RPC 与服务器通信
- **文档同步**: 实时同步文档变更和诊断
- **配置支持**: 可配置的诊断和完成选项

### 支持的 LSP 功能
- **文档同步**: 实时跟踪文件变更
- **诊断**: 错误和警告提示
- **代码完成**: 智能提示和自动完成
- **悬停信息**: 语法帮助和文档
- **配置管理**: 用户可自定义设置

### 新增命令
- `sline-highlight.restartServer`: 重启语言服务器
- `sline-highlight.helloWorld`: 测试命令

### 配置选项
- `slineLanguageServer.enableDiagnostics`: 启用/禁用诊断
- `slineLanguageServer.enableCompletion`: 启用/禁用代码完成
- `slineLanguageServer.maxNumberOfProblems`: 最大问题数量限制

### 开发体验改进
- 🔧 **调试支持**: 语言服务器调试配置
- 📝 **测试文件**: 创建 LSP 功能测试文件
- 🏗️ **构建优化**: 分离客户端和服务器编译

## [0.0.1] - 2025-07-09

### 新增功能
- ✨ 初始化 VSCode 插件项目结构
- 🎨 实现基础的 SLine 语言语法高亮
- 📝 添加语言配置文件，支持括号匹配和自动关闭
- 💬 支持单行注释 (`//`) 和多行注释 (`/* */`)
- 🔧 配置代码折叠功能
- 📦 设置项目构建和开发环境

### 技术实现
- 创建了 `package.json` 配置文件，定义插件基本信息和贡献点
- 实现了 `sline.tmLanguage.json` 语法规则文件
- 配置了 TypeScript 开发环境
- 添加了 VSCode 调试配置

### 支持的语法元素
- **关键字**: if, else, while, for, return, break, continue, function, var, let, const
- **字面量**: true, false, null, undefined  
- **字符串**: 双引号和单引号字符串，支持转义字符
- **数字**: 整数和浮点数
- **操作符**: 算术、比较、逻辑和赋值操作符
- **函数**: 函数名识别和高亮
- **注释**: 单行和多行注释

### 文件支持
- 支持 `.sline` 和 `.html` 文件扩展名

## [0.0.3] - 2025-07-09

### 重大更新
- 🔄 完全重写语法高亮规则，基于真实的 SLine 模板引擎语法
- 📚 深度分析 theme-sline 目录下的实际模板文件
- 🎯 精确匹配 Handlebars 风格的 SLine 语法

### 新增 Handlebars 风格语法支持
- **变量输出**: `{{variable}}` - 双大括号变量输出
- **不转义输出**: `{{{html_content}}}` - 三大括号不转义输出
- **块级指令**: `{{#if}}...{{/if}}`, `{{#each}}...{{/each}}`
- **组件调用**: `{{#component "name" /}}`
- **布局系统**: `{{#layout "theme" /}}`
- **内容区域**: `{{#content "section" /}}`
- **变量定义**: `{{#var name = value /}}`
- **模板注释**: `{{!-- comment --}}`

### 高级语法特性
- **过滤器系统**: `{{value | filter()}}`, `{{price | money}}`
- **Schema 配置**: `{{#schema}}...{{/schema}}`
- **条件分支**: `{{#if}}...{{#else}}...{{/if}}`
- **循环遍历**: `{{#each items}}...{{/each}}`
- **属性访问**: `{{object.property.subproperty}}`

### 支持的过滤器
- `asset_url`, `money`, `money_with_currency`
- `date`, `format`, `upper`, `lower`
- `truncate`, `default`, `class_list`
- `t` (翻译), `escape`, `strip_html`

### 技术实现
- 完全重构 `sline.tmLanguage.json` 语法规则
- 基于实际 theme-sline 项目的语法分析
- 优化语法匹配优先级和准确性
- 更新示例文件展示真实的 SLine 语法

## [0.0.2] - 2025-07-09

### 新增功能
- ✨ 添加模板引擎语法支持
- 🎨 支持 `.html` 文件中的 SLine 模板语法
- 📝 添加模板变量输出语法 `{{ variable }}`
- 🔧 添加模板控制块语法 `{% if %} {% endif %}`
- 💬 添加模板注释语法 `{# comment #}`
- 🔍 添加模板过滤器语法 `{{ variable | filter }}`

### 模板语法支持
- **变量输出**: `{{ username }}`, `{{ user.email }}`
- **控制结构**: `{% if %}`, `{% for %}`, `{% while %}` 等
- **模板注释**: `{# 这是注释 #}`
- **过滤器**: `{{ date | format('Y-m-d') }}`
- **嵌套语法**: 支持复杂的嵌套模板结构

### 技术改进
- 更新了 `sline.tmLanguage.json` 语法规则文件
- 添加了模板引擎专用的语法模式
- 创建了 HTML 模板示例文件
