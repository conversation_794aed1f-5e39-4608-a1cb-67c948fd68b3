{"scopeName": "source.handlebars.grammar", "$schema": "https://raw.githubusercontent.com/martinring/tmlanguage/master/tmlanguage.json", "name": "handlebars", "patterns": [{"include": "#meta_tags"}, {"include": "#html-attributes"}, {"include": "#keywords"}, {"include": "#tag"}, {"include": "#attribute"}, {"include": "#double_quoted_text"}, {"include": "#single_quoted_text"}, {"include": "#html_comments"}, {"include": "#handlebars_brackets"}, {"include": "#handlebars_variable"}], "repository": {"meta_tags": {"patterns": [{"name": "entity.name.tag", "match": "<\\/?[a-zA-Z][a-zA-Z0-9]*\\b"}]}, "html_tags": {"patterns": [{"begin": "(<)([a-zA-Z0-9:-]+)(?=[^>]*></\\2>)", "beginCaptures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.html"}}, "end": "(>(<)/)(\\2)(>)", "endCaptures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "meta.scope.between-tag-pair.html"}, "3": {"name": "entity.name.tag.html"}, "4": {"name": "punctuation.definition.tag.html"}}, "name": "meta.tag.any.html", "patterns": [{"include": "#tag-stuff"}]}, {"begin": "(<\\?)(xml)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.xml.html"}}, "end": "(\\?>)", "name": "meta.tag.preprocessor.xml.html", "patterns": [{"include": "#tag_generic_attribute"}, {"include": "#string"}]}, {"begin": "<!--", "captures": {"0": {"name": "punctuation.definition.comment.html"}}, "end": "--\\s*>", "name": "comment.block.html", "patterns": [{"match": "--", "name": "invalid.illegal.bad-comments-or-CDATA.html"}]}, {"begin": "<!", "captures": {"0": {"name": "punctuation.definition.tag.html"}}, "end": ">", "name": "meta.tag.sgml.html", "patterns": [{"begin": "(DOCTYPE|doctype)", "captures": {"1": {"name": "entity.name.tag.doctype.html"}}, "end": "(?=>)", "name": "meta.tag.sgml.doctype.html", "patterns": [{"match": "\"[^\">]*\"", "name": "string.quoted.double.doctype.identifiers-and-DTDs.html"}]}, {"begin": "\\[CDATA\\[", "end": "]](?=>)", "name": "constant.other.inline-data.html"}, {"match": "(\\s*)(?!--|>)\\S(\\s*)", "name": "invalid.illegal.bad-comments-or-CDATA.html"}]}, {"begin": "(?:^\\s+)?(<)((?i:style))\\b(?![^>]*/>)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.style.html"}, "3": {"name": "punctuation.definition.tag.html"}}, "end": "(</)((?i:style))(>)(?:\\s*\\n)?", "name": "source.css.embedded.html", "patterns": [{"include": "#tag-stuff"}, {"begin": "(>)", "beginCaptures": {"1": {"name": "punctuation.definition.tag.html"}}, "end": "(?=</(?i:style))", "patterns": [{"include": "source.css"}]}]}, {"begin": "(?:^\\s+)?(<)((?i:script))\\b(?![^>]*/>)", "beginCaptures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.script.html"}}, "end": "(?<=</(script|SCRIPT))(>)(?:\\s*\\n)?", "endCaptures": {"2": {"name": "punctuation.definition.tag.html"}}, "name": "source.js.embedded.html", "patterns": [{"include": "#tag-stuff"}, {"begin": "(?<!</(?:script|SCRIPT))(>)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.script.html"}}, "end": "(</)((?i:script))", "patterns": [{"captures": {"1": {"name": "punctuation.definition.comment.js"}}, "match": "(//).*?((?=</script)|$\\n?)", "name": "comment.line.double-slash.js"}, {"begin": "/\\*", "captures": {"0": {"name": "punctuation.definition.comment.js"}}, "end": "\\*/|(?=</script)", "name": "comment.block.js"}, {"include": "source.js"}]}]}, {"begin": "(</?)((?i:body|head|html)\\b)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.structure.any.html"}}, "end": "(>)", "name": "meta.tag.structure.any.html", "patterns": [{"include": "#tag-stuff"}]}, {"begin": "(</?)((?i:address|blockquote|dd|div|header|section|footer|aside|nav|dl|dt|fieldset|form|frame|frameset|h1|h2|h3|h4|h5|h6|iframe|noframes|object|ol|p|ul|applet|center|dir|hr|menu|pre)\\b)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.block.any.html"}}, "end": "(>)", "name": "meta.tag.block.any.html", "patterns": [{"include": "#tag-stuff"}]}, {"begin": "(</?)((?i:a|abbr|acronym|area|b|base|basefont|bdo|big|br|button|caption|cite|code|col|colgroup|del|dfn|em|font|head|html|i|img|input|ins|isindex|kbd|label|legend|li|link|map|meta|noscript|optgroup|option|param|q|s|samp|script|select|small|span|strike|strong|style|sub|sup|table|tbody|td|textarea|tfoot|th|thead|title|tr|tt|u|var)\\b)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.inline.any.html"}}, "end": "((?: ?/)?>)", "name": "meta.tag.inline.any.html", "patterns": [{"include": "#tag-stuff"}]}, {"begin": "(</?)([a-zA-Z0-9:-]+)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.other.html"}}, "end": "(>)", "name": "meta.tag.other.html", "patterns": [{"include": "#tag-stuff"}]}, {"begin": "(</?)([a-zA-Z0-9{}:-]+)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.tokenised.html"}}, "end": "(>)", "name": "meta.tag.tokenised.html", "patterns": [{"include": "#tag-stuff"}]}, {"include": "#entities"}, {"match": "<>", "name": "invalid.illegal.incomplete.html"}, {"match": "<", "name": "invalid.illegal.bad-angle-bracket.html"}]}, "html-attributes": {"match": "\\b([a-zA-Z][a-zA-Z0-9-_]*)\\b(?=\\s*=\\s*['\"])", "captures": {"1": {"name": "entity.other.attribute-name"}}}, "keywords": {"patterns": [{"name": "keyword.control.handlebars", "match": "\\b(?:if|with|each|inline|unless|@index|@key|as|lookup|log|level|compare)\\b"}]}, "single_quoted_text": {"begin": "'", "end": "'", "name": "string.quoted.single"}, "double_quoted_text": {"begin": "\"", "end": "\"", "name": "string.quoted.double"}, "html_comments": {"name": "comment.html", "begin": "<!--", "end": "-->"}, "handlebars_brackets": {"patterns": [{"match": "[{}]", "name": "support.constant"}]}, "handlebars_variable": {"patterns": [{"match": "\\b{{ ([^#/0-9][a-zA-Z_0-9]*) }}\\b", "name": "variable.parameter"}]}}}