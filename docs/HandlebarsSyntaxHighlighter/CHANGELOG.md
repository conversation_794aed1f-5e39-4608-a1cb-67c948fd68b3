## Release 1.1.2 (2023-12-13) - skipped because of network issue while deploying
## Release 1.1.1 (2023-12-13) - skipped because of network issue while deploying

## Release 1.1.0 (2023-12-13)

This is a regular release.

### Special Instructions

n/a

### General Enhancements

n/a

### Enhancements

* [issue #11](https://github.com/CarlSaqui29/HandlebarsSyntaxHighlighter/commit/1eb157870a95dc9c82083a60670e30adcc861b57) As a extension user I can clearly see highlighted conditional blocks
* [issue #9](https://github.com/CarlSaqui29/HandlebarsSyntaxHighlighter/commit/aba8efc47331a7600d0ad44859a2830a1f5bebc0) As the creator of this extension I should control all syntax highlighting and not rely on the language server identifier
* [issue #6](https://github.com/CarlSaqui29/HandlebarsSyntaxHighlighter/commit/79f96828a9afff3bb1335dfb0bce3e47eab8cc5b) As a extension user I can use shortcuts to create handlebars template and handlebars partial template
* [issue #4](https://github.com/CarlSaqui29/HandlebarsSyntaxHighlighter/commit/5a233570ee0c455dbb19c095e4bb0664c5b97319) Fix: html id attribute inline with the handlebars template syntax not highlighted
* [issue #3](https://github.com/CarlSaqui29/HandlebarsSyntaxHighlighter/commit/127632dd23040df05087352bddde029d78546ef1) Fix: handlebars highlighted syntax not recognize inside partial template


## Release 1.0.0 (2023-12-13)

This is a regular release.

### Special Instructions

n/a

### General Enhancements

n/a

### Enhancements

initialial Release