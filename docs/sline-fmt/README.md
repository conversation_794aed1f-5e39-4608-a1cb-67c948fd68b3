# sline-fmt

## 安装

```shell
pnpm add @sl/sline-html-parser # peerDependencies
pnpm add @sl/sline-fmt
```

## 示例

+ 支持 `esm` / `cjs` 引用
+ 支持 `Typescript`

```ts
import { slineFmt, FmtConfig } from '@sl/sline-fmt'

function main() {
  const config: FmtConfig = { tabWidth: 2, useTab: false }
  const formatted = slineFmt(`
<!doctype html>
<html lang="en">
  <head><title>{{ title }}</title></head>
  <body>
    <ul>
      {{#for item in users}}
        <li>{{item.name}}</li>
      {{/for}}
    </ul>
  </body>
</html>
`, config)
  console.log(formatted)
}

main()
```


