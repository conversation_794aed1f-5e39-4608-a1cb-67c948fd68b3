<div class="section-blog-container color-{{ section.settings.color_scheme.id }}"
  id="{{ aa }}"
  style="width: 10px; height: 20px; color: #fff; position: absolute; top: 0; left: 0">
  {{#component
    "xx"
    style="{width: 10, height: 20, color: '#fff', position: 'absolute', top: 0, left: 0}"
    a=1.2
    b=title
  /}}
  <span>{{#component
    "xx"
    style="{width: 10, height: 20, color: '#fff', position: 'absolute', top: 0, left: 0}"
    a=1.2
    b=title
  /}}</span>
  <div>
    {{#component
      "xx"
      style="{width: 10, height: 20, color: '#fff', position: 'absolute', top: 0, left: 0}"
      a=1.2
      b=title
    /}}
  </div>
  <div>
    {{#if a == 2 }}
      {{#component
        "xx"
        style="{width: 10, height: 20, color: '#fff', position: 'absolute', top: 0, left: 0}"
        a=1.2
        b=title
      /}}
    {{#else if a == 3 /}}
      {{#component
        "xx"
        style="{width: 10, height: 20, color: '#fff', position: 'absolute', top: 0, left: 0}"
        a=1.2
        b=title
      /}}
    {{#else/}}
      {{#component
        "xx"
        style="{width: 10, height: 20, color: '#fff', position: 'absolute', top: 0, left: 0}"
        a=1.2
        b=title
      /}}
    {{/if}}
  </div>
  <div>
    {{#switch a }}
    {{#case 3 /}}
      {{#component
        "xx"
        style="{width: 10, height: 20, color: '#fff', position: 'absolute', top: 0, left: 0}"
        a=1.2
        b=title
      /}}
    {{#case 1 2 /}}
      {{#component
        "xx"
        style="{width: 10, height: 20, color: '#fff', position: 'absolute', top: 0, left: 0}"
        a=1.2
        b=title
      /}}
      {{#component
        "xx"
        style="{width: 10, height: 20, color: '#fff', position: 'absolute', top: 0, left: 0}"
        a=1.2
        b=title
      /}}
    {{/switch}}
  </div>
  <div class="page-width">
    <div class="{{ section.settings | class_list() }}">
      {{#blocks}}
        {{#if forblock.type == "heading-with-link/heading-with-link" }}
          {{#block url=section.settings.blog.url /}}
        {{#else if forblock.type == "$articles" /}}
          {{#block blog=section.settings.blog /}}
        {{/if}}
      {{/blocks}}
    </div>
  </div>
</div>