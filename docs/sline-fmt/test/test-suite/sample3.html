{{#component "header" key1=(arr|size() > 0 || (b == "foo" && c )) key2="foo" /}}

{{#var v = arr|size() > 0 || (b == "foo" && c ) /}}

<ul>
  {{#for i in v }}
    <li>{{i}}</li>
  {{/for}}
</ul>

{{#if arr|size() > 0 || (b == "foo" && c ) }}
  OK
{{/if}}

{{#var val = "apple" /}}

{{#switch val  }}
{{#case "cat"   /}}
  animal
{{#case "apple"  "banana" /}}
  fruit
{{#else  /}}
  unknown
{{/switch  }}

{{#var enable = false /}}

{{#if a > 10}}
  {{#set enable = true /}}
{{/if}}

{{enable}} => true

{{#var flag = true /}}

{{#capture content}}
  test capture
  {{#if flag}}
    OK
  {{#else/}}
    FAIL
  {{/if}}
{{/capture}}

<div>
  {{content}}
  {{!-- @ignore-fmt 忽略下一行的样式格式化 --}}
  {{content}}
</div>

{{!-- 自定义标签 - block --}}
{{#product_form product id="xxxxx"}}
  <div>{{"11"}}</div>
{{/product_form}}

{{!-- for 标签 break / continue 的使用 --}}
{{#for item in arr}}
  {{#if forloop.index == 3}}
    {{#break/}}
  {{#else if forloop.index == 4/}}
    {{#continue/}}
  {{/if}}
  {{item}}
{{/for}}

11

