<div class="section-blog-container color-{{section.settings.color_scheme.id}}">
<div class="page-width">
  <div class="{{ section.settings | class_list() }}">
    {{#blocks}}
    <div>
      {{#if forblock.type == "heading-with-link/heading-with-link"}}
        <ul>
          <li>333</li>
          <li>{{xx.xx}}</li>
          <li><span>{{xx.xx}}</span></li>
        </ul>
        <div>123</div>
        {{#block url=section.settings.blog.url /}}
        {{#else if forblock.type == "$articles" /}}
        <div>333</div>
        {{#else if forblock.type == "xxx" /}}
        <div>444</div>
        <div>{{#block blog=section.settings.blog gg=xxx /}}</div>
            {{ xx }}
        {{/if}}
      {{"<div>123123\n</div>"}}
      </div>
      {{/blocks}}
            {{ xx }}
  </div>
  </div>
</div>