{"name": "@sl/sline-fmt", "version": "1.0.0-beta.4", "description": "", "keywords": [], "files": ["dist", "test"], "devDependencies": {"@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.26.0", "@jest/globals": "^29.7.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-typescript": "^12.1.2", "@types/jest": "^29.5.14", "@types/js-beautify": "^1.14.3", "@types/node": "^22.13.10", "babel-plugin-module-resolver": "^5.0.2", "jest": "^29.7.0", "rollup": "4.35.0", "rollup-plugin-cleanup": "^3.2.1", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.8.2"}, "repository": {"type": "git", "url": "https://git.inshopline.com/shopline/center-1/website/archive/sl-dev-kit/sline-fmt"}, "peerDependencies": {"@sl/sline-html-parser": "^0.2.7"}, "exports": {".": {"types": "./dist/fmt/types/index.d.ts", "require": "./dist/fmt/cjs/index.js", "import": "./dist/fmt/esm/index.js"}}, "dependencies": {"js-beautify": "^1.15.4", "parse5": "^7.3.0"}, "publishConfig": {"registry": "https://npm-registry.inshopline.com"}, "scripts": {"test": "jest", "build": "npm run rm:dist && tsc -p tsconfig.fmt.cjs.json && tsc -p tsconfig.fmt.esm.json", "rm:dist": "rm -rf ./dist"}}