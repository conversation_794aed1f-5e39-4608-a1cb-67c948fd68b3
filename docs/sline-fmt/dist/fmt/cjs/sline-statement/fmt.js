"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.slineFmt = slineFmt;
const parse5_1 = require("parse5");
const sline_html_parser_1 = require("@sl/sline-html-parser");
const value_1 = require("./formatter/value");
const var_1 = require("./formatter/var");
const if_1 = require("./formatter/if");
const block_tag_close_1 = require("./formatter/block-tag-close");
const for_1 = require("./formatter/for");
const switch_1 = require("./formatter/switch");
const case_1 = require("./formatter/case");
const set_1 = require("./formatter/set");
const else_1 = require("./formatter/else");
const comment_1 = require("./formatter/comment");
const text_node_1 = require("./formatter/text-node");
const capture_1 = require("./formatter/capture");
const default_tags_1 = require("./formatter/default-tags");
const utils_1 = require("./utils");
function completeConfig(config, logger, ast, index, indent) {
    return {
        printWidth: config.printWidth ?? 120,
        tabWidth: config.tabWidth,
        useTab: config.useTab,
        ast,
        index,
        parentIndent: indent,
        logger,
    };
}
class IndentCounter {
    current;
    unit;
    unitWidth;
    length;
    constructor(config, current) {
        this.current = current;
        this.unit = (0, utils_1.genIndentUnit)(config);
        this.unitWidth = this.unit.length;
        this.length = this.current.split(this.unit).length - 1;
    }
    isInScript(text) {
        if (/<script/.test(text)) {
            const htmlFragmentAST = (0, parse5_1.parseFragment)(text);
            let flatLastNode = (0, utils_1.last)(htmlFragmentAST.childNodes);
            while (flatLastNode) {
                // 最后一个是 script 标签, 则认为当前在
                if (flatLastNode.nodeName === 'script') {
                    return true;
                }
                else if ('childNodes' in flatLastNode) {
                    flatLastNode = (0, utils_1.last)(flatLastNode.childNodes);
                    continue;
                }
                break;
            }
        }
        return false;
    }
    canIndent(ast, i) {
        let prev = ast[i - 1];
        if (prev?.type === sline_html_parser_1.NodeTypes.TextNode) {
            if (this.isInScript(prev.raw)) {
                return false;
            }
            return /\n+ *$/.test(prev.raw);
        }
        return false;
    }
    add(ast, i) {
        if (this.canIndent(ast, i)) {
            this.current += this.unit;
            this.length++;
        }
    }
    subtract() {
        this.current = this.current.slice(0, -this.unitWidth);
        this.length--;
    }
    get() {
        return this.current;
    }
    formatCurrent(text, ast, i) {
        return this.canIndent(ast, i) ? this.current + text : text;
    }
    formatPrev(text, ast, i) {
        return this.canIndent(ast, i) ? this.current.slice(0, -this.unitWidth) + text : text;
    }
    formatTextNode(text) {
        if (this.length && !this.isInScript(text)) {
            let suffix = '';
            let li = text.search(/\n+ *$/);
            if (li !== -1) {
                suffix = text.slice(li);
                text = text.slice(0, li);
            }
            const ms = text.matchAll(/\n+/g);
            let m = ms.next();
            if (m.done) {
                return text + suffix;
            }
            let tt = text.slice(0, m.value.index);
            while (!m.done) {
                let m2 = ms.next();
                if (m2.done) {
                    tt += `\n${this.current}` + text.slice(m.value.index + m.value[0].length);
                    break;
                }
                else {
                    tt += `\n${this.current}` + text.slice(m.value.index + m.value[0].length, m2.value.index);
                }
                m = m2;
            }
            return tt + suffix;
        }
        return text;
    }
}
function slineFmt(src, config) {
    const logger = (0, utils_1.wrapLogger)(config.logger ?? (0, utils_1.defaultLogger)());
    let newSrc = '';
    const ast = (0, sline_html_parser_1.toSlineAST)(src);
    const l = ast.length;
    const indentCounters = [];
    let level = 0;
    let indentCounter = new IndentCounter(config, '');
    for (let i = 0; i < l; i++) {
        const token = ast[i];
        if ('statement' in token && token.statement.error) {
            logger.error(token.statement.error);
            newSrc += token.loc.source;
            continue;
        }
        switch (token.type) {
            case sline_html_parser_1.NodeTypes.TextNode:
                newSrc += indentCounter.formatTextNode((0, text_node_1.textNodeFmt)(token, logger));
                break;
            case sline_html_parser_1.NodeTypes.SlineValue:
                newSrc += indentCounter.formatCurrent((0, value_1.valueFmt)(token, logger), ast, i);
                break;
            case sline_html_parser_1.NodeTypes.SlineSelfCloseTag:
                switch (token.name) {
                    case 'var':
                        newSrc += indentCounter.formatCurrent((0, var_1.varFmt)(token, logger), ast, i);
                        break;
                    case 'set':
                        newSrc += indentCounter.formatCurrent((0, set_1.setFmt)(token, logger), ast, i);
                        break;
                    case 'case':
                        newSrc += indentCounter.formatPrev((0, case_1.caseFmt)(token, logger), ast, i);
                        break;
                    case 'else':
                        newSrc += indentCounter.formatPrev((0, else_1.elseFmt)(token, logger), ast, i);
                        break;
                    default:
                        newSrc += indentCounter.formatCurrent((0, default_tags_1.defaultTagsFmt)(token, completeConfig(config, logger, ast, i, indentCounter.get())), ast, i);
                }
                break;
            case sline_html_parser_1.NodeTypes.SlineBlockTagOpen:
                const nextIndentCounter = new IndentCounter(config, (0, utils_1.genIndentUnit)(config).repeat(level));
                indentCounters.push(nextIndentCounter);
                indentCounter = nextIndentCounter;
                switch (token.name) {
                    case 'if':
                        newSrc += indentCounter.formatCurrent((0, if_1.ifFmt)(token, logger), ast, i);
                        break;
                    case 'for':
                        newSrc += indentCounter.formatCurrent((0, for_1.forFmt)(token, logger), ast, i);
                        break;
                    case 'switch':
                        newSrc += indentCounter.formatCurrent((0, switch_1.switchFmt)(token, logger), ast, i);
                        break;
                    case 'capture':
                        newSrc += indentCounter.formatCurrent((0, capture_1.captureFmt)(token, logger), ast, i);
                        break;
                    default:
                        newSrc += indentCounter.formatCurrent((0, default_tags_1.defaultTagsFmt)(token, completeConfig(config, logger, ast, i, indentCounter.get())), ast, i);
                }
                indentCounter.add(ast, i);
                level++;
                break;
            case sline_html_parser_1.NodeTypes.SlineBlockTagClose:
                level--;
                indentCounter.subtract();
                indentCounters.pop();
                if (indentCounters.length > 0) {
                    indentCounter = indentCounters[indentCounters.length - 1];
                    newSrc += indentCounter.formatCurrent((0, block_tag_close_1.blockTagCloseFmt)(token, logger), ast, i);
                }
                else {
                    newSrc += (0, block_tag_close_1.blockTagCloseFmt)(token, logger);
                }
                break;
            case sline_html_parser_1.NodeTypes.SlineComment:
                const [c, ignoreNextLine] = (0, comment_1.commentFmt)(token, logger);
                newSrc += c;
                if (ignoreNextLine) {
                    (0, comment_1.handleIgnoreNext)(ast, i);
                }
                break;
            // ignore 触发场景最少, 尽量往后放
            case 'ExpansionIgnore':
                newSrc += indentCounter.formatCurrent((0, comment_1.ignoreFmt)(token, logger), ast, i);
                break;
            default:
                // @ts-ignore 容错
                logger.error(`not support node type: ${token.loc.source}`);
                // @ts-ignore 容错
                newSrc += token.loc.source;
                break;
        }
    }
    return newSrc;
}
