"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.elseFmt = void 0;
const expression_1 = require("./expression");
const elseFmt = (token, logger) => {
    // 有 if 开头则表示这是个 else if 标签
    if (token.statement.if) {
        return `{{${token.strip.start}#else if ${(0, expression_1.expressionTokenFmt)(token.statement.condition, logger)} /${token.strip.end}}}`;
    }
    return `{{${token.strip.start}#else/${token.strip.end}}}`;
};
exports.elseFmt = elseFmt;
