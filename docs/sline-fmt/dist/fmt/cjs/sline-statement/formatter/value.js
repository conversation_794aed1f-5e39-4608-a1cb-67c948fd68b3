"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.valueFmt = void 0;
const expression_1 = require("./expression");
const valueFmt = (slineStat, logger) => {
    if (slineStat.value.error) {
        logger.error(slineStat.value.error);
        return slineStat.loc.source;
    }
    if (slineStat.escape) {
        return `{{${slineStat.strip.start} ${(0, expression_1.expressionTokenFmt)(slineStat.value, logger)} ${slineStat.strip.end}}}`;
    }
    return `{{{${slineStat.strip.start} ${(0, expression_1.expressionTokenFmt)(slineStat.value, logger)} ${slineStat.strip.end}}}}`;
};
exports.valueFmt = valueFmt;
