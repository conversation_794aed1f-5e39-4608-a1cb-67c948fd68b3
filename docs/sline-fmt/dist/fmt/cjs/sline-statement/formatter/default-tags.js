"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.defaultTagsFmt = void 0;
exports.breakLine = breakLine;
const sline_html_parser_1 = require("@sl/sline-html-parser");
const expression_1 = require("./expression");
const utils_1 = require("../utils");
// 判断是否 break
function breakLine(config) {
    const current = config.ast[config.index];
    let len = current.loc.end - current.loc.start;
    let i = config.index;
    while (len <= config.printWidth && i > 0) {
        i--;
        const prev = config.ast[i];
        if (prev.type === sline_html_parser_1.NodeTypes.TextNode) {
            const matched = config.useTab
                ? /\n\t*$/.exec(prev.raw)
                : /\n *$/.exec(prev.raw);
            if (matched) {
                len += prev.raw.length - matched.index - 1;
                break;
            }
            else {
                len += prev.raw.length;
            }
        }
        else {
            len += prev.loc.end - prev.loc.start;
        }
    }
    i = config.index;
    const lastIndex = config.ast.length - 1;
    while (len <= config.printWidth && i < lastIndex) {
        i++;
        const next = config.ast[i];
        if (next.type === sline_html_parser_1.NodeTypes.TextNode) {
            const index = next.raw.indexOf('\n');
            if (index !== -1) {
                len += index;
                break;
            }
            else {
                len += next.raw.length;
            }
        }
        else {
            len += next.loc.end - next.loc.start;
        }
    }
    return len > config.printWidth;
}
function getIndentForMultiple(ast, index) {
    let i = index - 1;
    let prev = ast[i];
    while (prev) {
        if (prev.type === sline_html_parser_1.NodeTypes.TextNode) {
            const ms = prev.raw.matchAll(/\n+( *)/g);
            let m = undefined;
            for (let i of ms) {
                m = i;
            }
            if (m) {
                return m[1];
            }
        }
        i--;
        prev = ast[i];
    }
    return '';
}
const defaultTagsFmt = (t, config) => {
    const { strip: { start: ss, end: se } = { start: '', end: '' } } = t;
    const starts = `{{${ss}#${t.name}`;
    const ends = t.type === sline_html_parser_1.NodeTypes.SlineSelfCloseTag ? `/${se}}}` : `${se}}}`;
    if (t.statement.loc.source === '') {
        return `${starts}${ends}`;
    }
    else if (isDefaultTagStatement(t.statement)) {
        const p = t.statement.positionArgs.map(tt => (0, expression_1.expressionTokenFmt)(tt, config.logger));
        const a = t.statement.hashArgs.map(({ key, value }) => `${key}=${(0, expression_1.expressionTokenFmt)(value, config.logger)}`);
        let sf = '';
        if (p.length > 0) {
            sf += p.join(' ');
        }
        if (a.length > 0) {
            if (sf.length > 0) {
                sf += ' ';
            }
            sf += a.join(' ');
        }
        if (sf.length > config.printWidth || breakLine(config)) {
            const baseIndent = getIndentForMultiple(config.ast, config.index) + config.parentIndent;
            const indNext = baseIndent + (0, utils_1.genIndentUnit)(config);
            sf = '';
            if (p.length) {
                sf += `\n${indNext}${p.join('\n' + indNext)}`;
            }
            if (a.length) {
                sf += `\n${indNext}${a.join('\n' + indNext)}`;
            }
            return `${starts}${sf}\n${baseIndent}${ends}`;
        }
        return `${starts} ${sf} ${ends}`;
    }
    config.logger.error(`not support default tag: ${t.loc?.source}`);
    // 容错
    return t.loc.source;
};
exports.defaultTagsFmt = defaultTagsFmt;
function isDefaultTagStatement(s) {
    return s.hasOwnProperty('positionArgs') && s.hasOwnProperty('hashArgs');
}
