"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.expressionFmt = expressionFmt;
exports.expressionTokenFmt = expressionTokenFmt;
const sline_html_parser_1 = require("@sl/sline-html-parser");
function expressionFmt(expression, logger) {
    const expressionToken = (0, sline_html_parser_1.parseStrictExpression)(expression);
    return expressionTokenFmt(expressionToken, logger);
}
function expressionTokenFmt(t, logger) {
    switch (t.type) {
        case sline_html_parser_1.NodeTypes.Value:
            return tokenValueFmt(t, logger);
        case sline_html_parser_1.NodeTypes.BracketExpression:
            return `(${expressionTokenFmt(t.expression, logger)})`;
        case sline_html_parser_1.NodeTypes.BinaryExpression:
        case sline_html_parser_1.NodeTypes.LogicalExpression:
            return `${expressionTokenFmt(t.left, logger)} ${t.operator} ${expressionTokenFmt(t.right, logger)}`;
        case sline_html_parser_1.NodeTypes.NotExpression:
            return `!${expressionTokenFmt(t.value, logger)}`;
    }
    // @ts-ignore
    logger.error(`expressionTokenFmt: not support node type "${t.type}", source: ${t.loc.source}`);
    // @ts-ignore 容错 如果没有 match 的话返回原始代码
    return t.loc.source;
}
function tokenValueFmt(v, logger) {
    const arr = [];
    arr.push(tokenOperandFmt(v.operand, logger));
    if (v.filters?.length > 0) {
        arr.push(' | ' + filterArgsFmt(v.filters, logger));
    }
    return arr.join('');
}
function tokenOperandFmt(t, logger) {
    switch (t.type) {
        case sline_html_parser_1.NodeTypes.Lookup:
            return tokenLookupFmt(t, logger);
        case sline_html_parser_1.NodeTypes.BoolLiteral:
        case sline_html_parser_1.NodeTypes.FloatLiteral:
        case sline_html_parser_1.NodeTypes.IntegerLiteral:
        case sline_html_parser_1.NodeTypes.StringLiteral:
        case sline_html_parser_1.NodeTypes.NilLiteral:
            return t.loc.source.trim();
    }
    // @ts-ignore
    logger.error(`tokenOprandFmt: not support node type "${t.type}", source: ${t.loc.source}`);
    // @ts-ignore 容错
    return t.loc.source;
}
function tokenHashPairFmt(t, logger) {
    return `${t.key}=${tokenOperandFmt(t.value, logger)}`;
}
function tokenLookupFmt(v, logger) {
    return v.parts.map((p, i) => {
        switch (p.type) {
            case sline_html_parser_1.NodeTypes.Identifier:
                if (i === 0) {
                    return p.key;
                }
                return '.' + p.key;
            case sline_html_parser_1.NodeTypes.Lookup:
                return `[${tokenLookupFmt(p, logger)}]`;
            case sline_html_parser_1.NodeTypes.StringLiteral:
            case sline_html_parser_1.NodeTypes.IntegerLiteral:
                return `[${p.loc.source.trim()}]`;
        }
        // @ts-ignore
        logger.error(`tokenLookupFmt: not support parts node type "${p.type}", source: ${p.loc.source}`);
        // @ts-ignore 容错
        return p.loc.source;
    }).join('');
}
function filterArgsFmt(filters, logger) {
    const fs = [];
    for (const f of filters) {
        if (f.args.length > 0) {
            const args = [];
            for (const arg of f.args) {
                switch (arg.type) {
                    case sline_html_parser_1.NodeTypes.HashPair:
                        args.push(tokenHashPairFmt(arg, logger));
                        break;
                    default:
                        args.push(tokenOperandFmt(arg, logger));
                }
            }
            fs.push(`${f.name}(${args.join(', ')})`);
        }
        else {
            fs.push(`${f.name}()`);
        }
    }
    return fs.join(' | ');
}
