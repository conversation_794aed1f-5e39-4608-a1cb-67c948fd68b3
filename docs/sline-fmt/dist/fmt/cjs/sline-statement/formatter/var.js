"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.varFmt = void 0;
const expression_1 = require("./expression");
const varFmt = (token, logger) => {
    const name = token.statement.key;
    if (token.statement.value) {
        return `{{${token.strip.start}#var ${name} = ${(0, expression_1.expressionTokenFmt)(token.statement.value, logger)} /${token.strip.end}}}`;
    }
    return `{{${token.strip.start}#var ${name} /${token.strip.end}}}`;
};
exports.varFmt = varFmt;
