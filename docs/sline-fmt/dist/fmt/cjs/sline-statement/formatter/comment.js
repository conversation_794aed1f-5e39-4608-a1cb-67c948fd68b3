"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.commentFmt = exports.ignoreFmt = void 0;
exports.handleIgnoreNext = handleIgnoreNext;
const sline_html_parser_1 = require("@sl/sline-html-parser");
const ignorePrefix = /^\s*@ignore-fmt/;
/** ignore Token 的导出 */
const ignoreFmt = (t) => {
    // 直接导出原始代码
    return t.loc.source;
};
exports.ignoreFmt = ignoreFmt;
/** 忽略下一个 sline token 的格式化 */
function handleIgnoreNext(ast, i) {
    const nextToken = ast[i + 1];
    if (!nextToken) {
        return;
    }
    if (nextToken.type === sline_html_parser_1.NodeTypes.TextNode) {
        if (/^\s*$/.test(nextToken.raw)) {
            let count = 0;
            for (let v of nextToken.raw) {
                if (v === '\n') {
                    count++;
                    if (count > 1) {
                        break;
                    }
                }
            }
            // 只有一个换行 ignore 才生效
            if (count === 1) {
                if (ast[i + 2]) {
                    ast[i + 2] = toIgnoreToken(ast[i + 2]);
                }
            }
        }
    }
    else {
        ast[i + 1] = toIgnoreToken(ast[i + 1]);
    }
}
// 将当前的 sline token 转化成 ignore token
// slineFmt() 只有在检测到该 token 时才生效, 这样可以减少频繁判断是否要格式化的判断
function toIgnoreToken(token) {
    // 因为 comment 内部有 ignore 逻辑需要执行, 不能被简单忽略忽略, 增加 ignore 属性, 用于忽略格式化, 但执行其它功能逻辑
    if (token.type === sline_html_parser_1.NodeTypes.SlineComment) {
        token.ignore = true;
        return token;
    }
    return { type: 'ExpansionIgnore', loc: token.loc };
}
function fmtCommentContent(t) {
    return `{{${t.strip.start}!--${t.content}--${t.strip.end}}}`;
}
function isCommentWithIgnore(t) {
    return ignorePrefix.test(t.content);
}
const commentFmt = (t) => {
    return [
        // ignore 为 true 时, 忽略格式化, 但保留是否忽略下一行格式的判断
        t.ignore ? t.loc.source : fmtCommentContent(t),
        isCommentWithIgnore(t),
    ];
};
exports.commentFmt = commentFmt;
