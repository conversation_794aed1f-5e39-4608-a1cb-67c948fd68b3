"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setFmt = void 0;
const expression_1 = require("./expression");
const setFmt = (token, logger) => {
    const name = token.statement.key;
    const expression = (0, expression_1.expressionTokenFmt)(token.statement.value, logger);
    return `{{${token.strip.start}#set ${name} = ${expression} /${token.strip.end}}}`;
};
exports.setFmt = setFmt;
