"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.genIndentUnit = genIndentUnit;
exports.last = last;
exports.defaultLogger = defaultLogger;
exports.wrapLogger = wrapLogger;
function genIndentUnit(config) {
    return (config.useTab ? '\t' : ' ').repeat(config.tabWidth);
}
function last(arr) {
    return arr[arr.length - 1];
}
function defaultLogger() {
    return {
        info: console.log,
        warn: console.warn,
        error: console.error,
    };
}
function wrapLogger(logger) {
    const slineFmtTag = `[sline-fmt]`;
    function messageToString(message) {
        if (!message) {
            return `${message}`;
        }
        else if (typeof message === 'string') {
            return message;
        }
        else if (typeof message === 'function') {
            return messageToString(message());
        }
        return JSON.stringify(message);
    }
    function logUnknow(e) {
        try {
            logger.error(`${slineFmtTag}: unknow error! ${e}`);
        }
        catch (ee) {
            console.error(`${slineFmtTag}:`, e, ee);
        }
    }
    return {
        info: (message) => {
            try {
                logger.info(`${slineFmtTag}: ${messageToString(message)}`);
            }
            catch (e) {
                logUnknow(e);
            }
        },
        warn: (message) => {
            try {
                logger.warn(`${slineFmtTag}: ${messageToString(message)}`);
            }
            catch (e) {
                logUnknow(e);
            }
        },
        error: (message) => {
            try {
                logger.error(`${slineFmtTag}: ${messageToString(message)}`);
            }
            catch (e) {
                logUnknow(e);
            }
        },
    };
}
