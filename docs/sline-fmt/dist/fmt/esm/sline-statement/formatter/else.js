import { expressionTokenFmt } from './expression';
export var elseFmt = function (token, logger) {
    // 有 if 开头则表示这是个 else if 标签
    if (token.statement.if) {
        return "{{".concat(token.strip.start, "#else if ").concat(expressionTokenFmt(token.statement.condition, logger), " /").concat(token.strip.end, "}}");
    }
    return "{{".concat(token.strip.start, "#else/").concat(token.strip.end, "}}");
};
