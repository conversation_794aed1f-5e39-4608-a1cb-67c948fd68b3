import { expressionTokenFmt } from './expression';
export var varFmt = function (token, logger) {
    var name = token.statement.key;
    if (token.statement.value) {
        return "{{".concat(token.strip.start, "#var ").concat(name, " = ").concat(expressionTokenFmt(token.statement.value, logger), " /").concat(token.strip.end, "}}");
    }
    return "{{".concat(token.strip.start, "#var ").concat(name, " /").concat(token.strip.end, "}}");
};
