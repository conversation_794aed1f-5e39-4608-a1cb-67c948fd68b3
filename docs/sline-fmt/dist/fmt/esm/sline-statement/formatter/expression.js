var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
import { NodeTypes, parseStrictExpression } from '@sl/sline-html-parser';
export function expressionFmt(expression, logger) {
    var expressionToken = parseStrictExpression(expression);
    return expressionTokenFmt(expressionToken, logger);
}
export function expressionTokenFmt(t, logger) {
    switch (t.type) {
        case NodeTypes.Value:
            return tokenValueFmt(t, logger);
        case NodeTypes.BracketExpression:
            return "(".concat(expressionTokenFmt(t.expression, logger), ")");
        case NodeTypes.BinaryExpression:
        case NodeTypes.LogicalExpression:
            return "".concat(expressionTokenFmt(t.left, logger), " ").concat(t.operator, " ").concat(expressionTokenFmt(t.right, logger));
        case NodeTypes.NotExpression:
            return "!".concat(expressionTokenFmt(t.value, logger));
    }
    // @ts-ignore
    logger.error("expressionTokenFmt: not support node type \"".concat(t.type, "\", source: ").concat(t.loc.source));
    // @ts-ignore 容错 如果没有 match 的话返回原始代码
    return t.loc.source;
}
function tokenValueFmt(v, logger) {
    var _a;
    var arr = [];
    arr.push(tokenOperandFmt(v.operand, logger));
    if (((_a = v.filters) === null || _a === void 0 ? void 0 : _a.length) > 0) {
        arr.push(' | ' + filterArgsFmt(v.filters, logger));
    }
    return arr.join('');
}
function tokenOperandFmt(t, logger) {
    switch (t.type) {
        case NodeTypes.Lookup:
            return tokenLookupFmt(t, logger);
        case NodeTypes.BoolLiteral:
        case NodeTypes.FloatLiteral:
        case NodeTypes.IntegerLiteral:
        case NodeTypes.StringLiteral:
        case NodeTypes.NilLiteral:
            return t.loc.source.trim();
    }
    // @ts-ignore
    logger.error("tokenOprandFmt: not support node type \"".concat(t.type, "\", source: ").concat(t.loc.source));
    // @ts-ignore 容错
    return t.loc.source;
}
function tokenHashPairFmt(t, logger) {
    return "".concat(t.key, "=").concat(tokenOperandFmt(t.value, logger));
}
function tokenLookupFmt(v, logger) {
    return v.parts.map(function (p, i) {
        switch (p.type) {
            case NodeTypes.Identifier:
                if (i === 0) {
                    return p.key;
                }
                return '.' + p.key;
            case NodeTypes.Lookup:
                return "[".concat(tokenLookupFmt(p, logger), "]");
            case NodeTypes.StringLiteral:
            case NodeTypes.IntegerLiteral:
                return "[".concat(p.loc.source.trim(), "]");
        }
        // @ts-ignore
        logger.error("tokenLookupFmt: not support parts node type \"".concat(p.type, "\", source: ").concat(p.loc.source));
        // @ts-ignore 容错
        return p.loc.source;
    }).join('');
}
function filterArgsFmt(filters, logger) {
    var e_1, _a, e_2, _b;
    var fs = [];
    try {
        for (var filters_1 = __values(filters), filters_1_1 = filters_1.next(); !filters_1_1.done; filters_1_1 = filters_1.next()) {
            var f = filters_1_1.value;
            if (f.args.length > 0) {
                var args = [];
                try {
                    for (var _c = (e_2 = void 0, __values(f.args)), _d = _c.next(); !_d.done; _d = _c.next()) {
                        var arg = _d.value;
                        switch (arg.type) {
                            case NodeTypes.HashPair:
                                args.push(tokenHashPairFmt(arg, logger));
                                break;
                            default:
                                args.push(tokenOperandFmt(arg, logger));
                        }
                    }
                }
                catch (e_2_1) { e_2 = { error: e_2_1 }; }
                finally {
                    try {
                        if (_d && !_d.done && (_b = _c.return)) _b.call(_c);
                    }
                    finally { if (e_2) throw e_2.error; }
                }
                fs.push("".concat(f.name, "(").concat(args.join(', '), ")"));
            }
            else {
                fs.push("".concat(f.name, "()"));
            }
        }
    }
    catch (e_1_1) { e_1 = { error: e_1_1 }; }
    finally {
        try {
            if (filters_1_1 && !filters_1_1.done && (_a = filters_1.return)) _a.call(filters_1);
        }
        finally { if (e_1) throw e_1.error; }
    }
    return fs.join(' | ');
}
