var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
import { NodeTypes } from '@sl/sline-html-parser';
import { expressionTokenFmt } from './expression';
import { genIndentUnit } from '../utils';
// 判断是否 break
export function breakLine(config) {
    var current = config.ast[config.index];
    var len = current.loc.end - current.loc.start;
    var i = config.index;
    while (len <= config.printWidth && i > 0) {
        i--;
        var prev = config.ast[i];
        if (prev.type === NodeTypes.TextNode) {
            var matched = config.useTab
                ? /\n\t*$/.exec(prev.raw)
                : /\n *$/.exec(prev.raw);
            if (matched) {
                len += prev.raw.length - matched.index - 1;
                break;
            }
            else {
                len += prev.raw.length;
            }
        }
        else {
            len += prev.loc.end - prev.loc.start;
        }
    }
    i = config.index;
    var lastIndex = config.ast.length - 1;
    while (len <= config.printWidth && i < lastIndex) {
        i++;
        var next = config.ast[i];
        if (next.type === NodeTypes.TextNode) {
            var index = next.raw.indexOf('\n');
            if (index !== -1) {
                len += index;
                break;
            }
            else {
                len += next.raw.length;
            }
        }
        else {
            len += next.loc.end - next.loc.start;
        }
    }
    return len > config.printWidth;
}
function getIndentForMultiple(ast, index) {
    var e_1, _a;
    var i = index - 1;
    var prev = ast[i];
    while (prev) {
        if (prev.type === NodeTypes.TextNode) {
            var ms = prev.raw.matchAll(/\n+( *)/g);
            var m = undefined;
            try {
                for (var ms_1 = (e_1 = void 0, __values(ms)), ms_1_1 = ms_1.next(); !ms_1_1.done; ms_1_1 = ms_1.next()) {
                    var i_1 = ms_1_1.value;
                    m = i_1;
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (ms_1_1 && !ms_1_1.done && (_a = ms_1.return)) _a.call(ms_1);
                }
                finally { if (e_1) throw e_1.error; }
            }
            if (m) {
                return m[1];
            }
        }
        i--;
        prev = ast[i];
    }
    return '';
}
export var defaultTagsFmt = function (t, config) {
    var _a;
    var _b = t.strip, _c = _b === void 0 ? { start: '', end: '' } : _b, ss = _c.start, se = _c.end;
    var starts = "{{".concat(ss, "#").concat(t.name);
    var ends = t.type === NodeTypes.SlineSelfCloseTag ? "/".concat(se, "}}") : "".concat(se, "}}");
    if (t.statement.loc.source === '') {
        return "".concat(starts).concat(ends);
    }
    else if (isDefaultTagStatement(t.statement)) {
        var p = t.statement.positionArgs.map(function (tt) { return expressionTokenFmt(tt, config.logger); });
        var a = t.statement.hashArgs.map(function (_a) {
            var key = _a.key, value = _a.value;
            return "".concat(key, "=").concat(expressionTokenFmt(value, config.logger));
        });
        var sf = '';
        if (p.length > 0) {
            sf += p.join(' ');
        }
        if (a.length > 0) {
            if (sf.length > 0) {
                sf += ' ';
            }
            sf += a.join(' ');
        }
        if (sf.length > config.printWidth || breakLine(config)) {
            var baseIndent = getIndentForMultiple(config.ast, config.index) + config.parentIndent;
            var indNext = baseIndent + genIndentUnit(config);
            sf = '';
            if (p.length) {
                sf += "\n".concat(indNext).concat(p.join('\n' + indNext));
            }
            if (a.length) {
                sf += "\n".concat(indNext).concat(a.join('\n' + indNext));
            }
            return "".concat(starts).concat(sf, "\n").concat(baseIndent).concat(ends);
        }
        return "".concat(starts, " ").concat(sf, " ").concat(ends);
    }
    config.logger.error("not support default tag: ".concat((_a = t.loc) === null || _a === void 0 ? void 0 : _a.source));
    // 容错
    return t.loc.source;
};
function isDefaultTagStatement(s) {
    return s.hasOwnProperty('positionArgs') && s.hasOwnProperty('hashArgs');
}
