var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
import { NodeTypes } from '@sl/sline-html-parser';
var ignorePrefix = /^\s*@ignore-fmt/;
/** ignore Token 的导出 */
export var ignoreFmt = function (t) {
    // 直接导出原始代码
    return t.loc.source;
};
/** 忽略下一个 sline token 的格式化 */
export function handleIgnoreNext(ast, i) {
    var e_1, _a;
    var nextToken = ast[i + 1];
    if (!nextToken) {
        return;
    }
    if (nextToken.type === NodeTypes.TextNode) {
        if (/^\s*$/.test(nextToken.raw)) {
            var count = 0;
            try {
                for (var _b = __values(nextToken.raw), _c = _b.next(); !_c.done; _c = _b.next()) {
                    var v = _c.value;
                    if (v === '\n') {
                        count++;
                        if (count > 1) {
                            break;
                        }
                    }
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                }
                finally { if (e_1) throw e_1.error; }
            }
            // 只有一个换行 ignore 才生效
            if (count === 1) {
                if (ast[i + 2]) {
                    ast[i + 2] = toIgnoreToken(ast[i + 2]);
                }
            }
        }
    }
    else {
        ast[i + 1] = toIgnoreToken(ast[i + 1]);
    }
}
// 将当前的 sline token 转化成 ignore token
// slineFmt() 只有在检测到该 token 时才生效, 这样可以减少频繁判断是否要格式化的判断
function toIgnoreToken(token) {
    // 因为 comment 内部有 ignore 逻辑需要执行, 不能被简单忽略忽略, 增加 ignore 属性, 用于忽略格式化, 但执行其它功能逻辑
    if (token.type === NodeTypes.SlineComment) {
        token.ignore = true;
        return token;
    }
    return { type: 'ExpansionIgnore', loc: token.loc };
}
function fmtCommentContent(t) {
    return "{{".concat(t.strip.start, "!--").concat(t.content, "--").concat(t.strip.end, "}}");
}
function isCommentWithIgnore(t) {
    return ignorePrefix.test(t.content);
}
export var commentFmt = function (t) {
    return [
        // ignore 为 true 时, 忽略格式化, 但保留是否忽略下一行格式的判断
        t.ignore ? t.loc.source : fmtCommentContent(t),
        isCommentWithIgnore(t),
    ];
};
