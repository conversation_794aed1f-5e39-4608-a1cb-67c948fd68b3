import { expressionTokenFmt } from './expression';
export var valueFmt = function (slineStat, logger) {
    if (slineStat.value.error) {
        logger.error(slineStat.value.error);
        return slineStat.loc.source;
    }
    if (slineStat.escape) {
        return "{{".concat(slineStat.strip.start, " ").concat(expressionTokenFmt(slineStat.value, logger), " ").concat(slineStat.strip.end, "}}");
    }
    return "{{{".concat(slineStat.strip.start, " ").concat(expressionTokenFmt(slineStat.value, logger), " ").concat(slineStat.strip.end, "}}}");
};
