export function genIndentUnit(config) {
    return (config.useTab ? '\t' : ' ').repeat(config.tabWidth);
}
export function last(arr) {
    return arr[arr.length - 1];
}
export function defaultLogger() {
    return {
        info: console.log,
        warn: console.warn,
        error: console.error,
    };
}
export function wrapLogger(logger) {
    var slineFmtTag = "[sline-fmt]";
    function messageToString(message) {
        if (!message) {
            return "".concat(message);
        }
        else if (typeof message === 'string') {
            return message;
        }
        else if (typeof message === 'function') {
            return messageToString(message());
        }
        return JSON.stringify(message);
    }
    function logUnknow(e) {
        try {
            logger.error("".concat(slineFmtTag, ": unknow error! ").concat(e));
        }
        catch (ee) {
            console.error("".concat(slineFmtTag, ":"), e, ee);
        }
    }
    return {
        info: function (message) {
            try {
                logger.info("".concat(slineFmtTag, ": ").concat(messageToString(message)));
            }
            catch (e) {
                logUnknow(e);
            }
        },
        warn: function (message) {
            try {
                logger.warn("".concat(slineFmtTag, ": ").concat(messageToString(message)));
            }
            catch (e) {
                logUnknow(e);
            }
        },
        error: function (message) {
            try {
                logger.error("".concat(slineFmtTag, ": ").concat(messageToString(message)));
            }
            catch (e) {
                logUnknow(e);
            }
        },
    };
}
