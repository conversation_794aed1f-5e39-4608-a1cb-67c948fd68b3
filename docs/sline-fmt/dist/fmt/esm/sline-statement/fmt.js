var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
import { parseFragment } from 'parse5';
import { NodeTypes, toSlineAST } from '@sl/sline-html-parser';
import { valueFmt } from './formatter/value';
import { varFmt } from './formatter/var';
import { ifFmt } from './formatter/if';
import { blockTagCloseFmt } from './formatter/block-tag-close';
import { forFmt } from './formatter/for';
import { switchFmt } from './formatter/switch';
import { caseFmt } from './formatter/case';
import { setFmt } from './formatter/set';
import { elseFmt } from './formatter/else';
import { commentFmt, handleIgnoreNext, ignoreFmt } from './formatter/comment';
import { textNodeFmt } from './formatter/text-node';
import { captureFmt } from './formatter/capture';
import { defaultTagsFmt } from './formatter/default-tags';
import { defaultLogger, genIndentUnit, last, wrapLogger } from './utils';
function completeConfig(config, logger, ast, index, indent) {
    var _a;
    return {
        printWidth: (_a = config.printWidth) !== null && _a !== void 0 ? _a : 120,
        tabWidth: config.tabWidth,
        useTab: config.useTab,
        ast: ast,
        index: index,
        parentIndent: indent,
        logger: logger,
    };
}
var IndentCounter = /** @class */ (function () {
    function IndentCounter(config, current) {
        this.current = current;
        this.unit = genIndentUnit(config);
        this.unitWidth = this.unit.length;
        this.length = this.current.split(this.unit).length - 1;
    }
    IndentCounter.prototype.isInScript = function (text) {
        if (/<script/.test(text)) {
            var htmlFragmentAST = parseFragment(text);
            var flatLastNode = last(htmlFragmentAST.childNodes);
            while (flatLastNode) {
                // 最后一个是 script 标签, 则认为当前在
                if (flatLastNode.nodeName === 'script') {
                    return true;
                }
                else if ('childNodes' in flatLastNode) {
                    flatLastNode = last(flatLastNode.childNodes);
                    continue;
                }
                break;
            }
        }
        return false;
    };
    IndentCounter.prototype.canIndent = function (ast, i) {
        var prev = ast[i - 1];
        if ((prev === null || prev === void 0 ? void 0 : prev.type) === NodeTypes.TextNode) {
            if (this.isInScript(prev.raw)) {
                return false;
            }
            return /\n+ *$/.test(prev.raw);
        }
        return false;
    };
    IndentCounter.prototype.add = function (ast, i) {
        if (this.canIndent(ast, i)) {
            this.current += this.unit;
            this.length++;
        }
    };
    IndentCounter.prototype.subtract = function () {
        this.current = this.current.slice(0, -this.unitWidth);
        this.length--;
    };
    IndentCounter.prototype.get = function () {
        return this.current;
    };
    IndentCounter.prototype.formatCurrent = function (text, ast, i) {
        return this.canIndent(ast, i) ? this.current + text : text;
    };
    IndentCounter.prototype.formatPrev = function (text, ast, i) {
        return this.canIndent(ast, i) ? this.current.slice(0, -this.unitWidth) + text : text;
    };
    IndentCounter.prototype.formatTextNode = function (text) {
        if (this.length && !this.isInScript(text)) {
            var suffix = '';
            var li = text.search(/\n+ *$/);
            if (li !== -1) {
                suffix = text.slice(li);
                text = text.slice(0, li);
            }
            var ms = text.matchAll(/\n+/g);
            var m = ms.next();
            if (m.done) {
                return text + suffix;
            }
            var tt = text.slice(0, m.value.index);
            while (!m.done) {
                var m2 = ms.next();
                if (m2.done) {
                    tt += "\n".concat(this.current) + text.slice(m.value.index + m.value[0].length);
                    break;
                }
                else {
                    tt += "\n".concat(this.current) + text.slice(m.value.index + m.value[0].length, m2.value.index);
                }
                m = m2;
            }
            return tt + suffix;
        }
        return text;
    };
    return IndentCounter;
}());
export function slineFmt(src, config) {
    var _a;
    var logger = wrapLogger((_a = config.logger) !== null && _a !== void 0 ? _a : defaultLogger());
    var newSrc = '';
    var ast = toSlineAST(src);
    var l = ast.length;
    var indentCounters = [];
    var level = 0;
    var indentCounter = new IndentCounter(config, '');
    for (var i = 0; i < l; i++) {
        var token = ast[i];
        if ('statement' in token && token.statement.error) {
            logger.error(token.statement.error);
            newSrc += token.loc.source;
            continue;
        }
        switch (token.type) {
            case NodeTypes.TextNode:
                newSrc += indentCounter.formatTextNode(textNodeFmt(token, logger));
                break;
            case NodeTypes.SlineValue:
                newSrc += indentCounter.formatCurrent(valueFmt(token, logger), ast, i);
                break;
            case NodeTypes.SlineSelfCloseTag:
                switch (token.name) {
                    case 'var':
                        newSrc += indentCounter.formatCurrent(varFmt(token, logger), ast, i);
                        break;
                    case 'set':
                        newSrc += indentCounter.formatCurrent(setFmt(token, logger), ast, i);
                        break;
                    case 'case':
                        newSrc += indentCounter.formatPrev(caseFmt(token, logger), ast, i);
                        break;
                    case 'else':
                        newSrc += indentCounter.formatPrev(elseFmt(token, logger), ast, i);
                        break;
                    default:
                        newSrc += indentCounter.formatCurrent(defaultTagsFmt(token, completeConfig(config, logger, ast, i, indentCounter.get())), ast, i);
                }
                break;
            case NodeTypes.SlineBlockTagOpen:
                var nextIndentCounter = new IndentCounter(config, genIndentUnit(config).repeat(level));
                indentCounters.push(nextIndentCounter);
                indentCounter = nextIndentCounter;
                switch (token.name) {
                    case 'if':
                        newSrc += indentCounter.formatCurrent(ifFmt(token, logger), ast, i);
                        break;
                    case 'for':
                        newSrc += indentCounter.formatCurrent(forFmt(token, logger), ast, i);
                        break;
                    case 'switch':
                        newSrc += indentCounter.formatCurrent(switchFmt(token, logger), ast, i);
                        break;
                    case 'capture':
                        newSrc += indentCounter.formatCurrent(captureFmt(token, logger), ast, i);
                        break;
                    default:
                        newSrc += indentCounter.formatCurrent(defaultTagsFmt(token, completeConfig(config, logger, ast, i, indentCounter.get())), ast, i);
                }
                indentCounter.add(ast, i);
                level++;
                break;
            case NodeTypes.SlineBlockTagClose:
                level--;
                indentCounter.subtract();
                indentCounters.pop();
                if (indentCounters.length > 0) {
                    indentCounter = indentCounters[indentCounters.length - 1];
                    newSrc += indentCounter.formatCurrent(blockTagCloseFmt(token, logger), ast, i);
                }
                else {
                    newSrc += blockTagCloseFmt(token, logger);
                }
                break;
            case NodeTypes.SlineComment:
                var _b = __read(commentFmt(token, logger), 2), c = _b[0], ignoreNextLine = _b[1];
                newSrc += c;
                if (ignoreNextLine) {
                    handleIgnoreNext(ast, i);
                }
                break;
            // ignore 触发场景最少, 尽量往后放
            case 'ExpansionIgnore':
                newSrc += indentCounter.formatCurrent(ignoreFmt(token, logger), ast, i);
                break;
            default:
                // @ts-ignore 容错
                logger.error("not support node type: ".concat(token.loc.source));
                // @ts-ignore 容错
                newSrc += token.loc.source;
                break;
        }
    }
    return newSrc;
}
