var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
import * as jsBeautify from 'js-beautify';
export function htmlFmt(src, options) {
    if (options === void 0) { options = {}; }
    return jsBeautify.html(src, __assign(__assign({}, options), { indent_handlebars: false }));
}
