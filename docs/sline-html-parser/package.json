{"name": "@sl/sline-html-parser", "version": "0.2.7", "description": "Sline HTML parser by Shopline", "author": "", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["grammar/*", "dist/**/*.js", "dist/**/*.ts"], "scripts": {"clean": "rm -rf ./dist", "prebuild:ts": "node build/shims.js", "build": "npm run prebuild:ts & npm run clean && tsc", "test": "npm run prebuild:ts && jest"}, "dependencies": {"ohm-js": "^16.3.0"}, "devDependencies": {"@types/jest": "^27.4.0", "@types/node": "14", "jest": "^27.4.7", "jest-junit": "^13.0.0", "ts-jest": "^27.1.3", "ts-node": "^10.4.0", "typescript": "^4.5.5"}}