Common {
  node = textNode
  textNode = (~ openControl any)+
  
  openControl = end

  anyExceptStar<lit> = (~ lit any)*
  anyExceptPlus<lit> = (~ lit any)+
  AnyExceptPlus<lit> = (~ lit any)+
  controls = "\u{007F}".."\u{009F}"
  noncharacters = "\u{FDD0}".."\u{FDEF}"
  identifier = (letter | "_") (alnum | "_")*
  newline = "\r"? "\n"
}

Expression <: Common {
    expression
    = logicalExpression
    | binaryExpression
    | bracketExpression<expression>
    | notExpression
    | value
  
  expressionWithRest = space* expression? any*

  bracketExpression<expr> = "(" space* expr space* ")"
  logicalExpression = expression space* logicalOperator space* (bracketExpression<expression> | notExpression | binaryExpression | value)
  notExpression = "!" (bracketExpression<expression> | value)
  
  binaryOperand = bracketExpression<binaryOperand> | value
  binaryExpression = binaryOperand space* binaryOperator space* binaryOperand

  value = operand filter*

  filter = space* "|" space* identifier filterArgs
  filterArgs
    = filterPositionArgs
    | filterHashArgs
    | filterPositionAndHashArgs
    | filterNoArgs
  filterPositionArgs = "(" space* nonemptyListOf<operand, argsSep> space* ")"
  filterHashArgs = "(" space* nonemptyListOf<hashPair<identifier, operand>, argsSep> space* ")"
  filterPositionAndHashArgs = "(" space* (operand argsSep)+ nonemptyListOf<hashPair<identifier, operand>, argsSep> space* ")"
  filterNoArgs = "(" space* ")"
  argsSep = space* "," space*
  hashPair<k, v> = k space* "=" space* v

  operand = literal | lookup

  dotLookup = "." identifier
  indexLookup = "[" (lookup | integerLiteral | stringLiteral) "]"
  lookup = identifier (dotLookup | indexLookup)*

  literal
    = nilLiteral
    | boolLiteral
    | floatLiteral
    | integerLiteral
    | stringLiteral

  nilLiteral = "nil"
  boolLiteral = "true" | "false"
  floatLiteral = digit* "." digit+
  integerLiteral = digit+
  
  singleQuote = "'"
  doubleQuote = "\""
  backquote = "`"
  slineQuotes = singleQuote | doubleQuote | backquote
  stringLiteral
  	= doubleQuote ("\\\""|(~ doubleQuote any))* doubleQuote
  	| singleQuote ("\\'"|(~ singleQuote any))* singleQuote
  	| backquote ("\\`"|(~ backquote any))* backquote

  unaryOperator = "!"
  
  binaryOperator
    = "=="
    | "!="
    | ">="
    | "<="
    | ">"
    | "<"

  logicalOperator
    = "||"
    | "&&"
}

Sline <: Expression {
  node := (textNode | slineNode)*

  openControl := "{{"

  slineNode
    = slineSelfCloseTag
    | slineBlockTagOpen
    | slineBlockTagClose
    | slineComment
    | slineValue

  slineValueExpression<end> = (stringLiteral | anyExceptPlus<(end | slineQuotes)>)*

  slineValue = slineRawValue | slineEscapeValue

  escapeValueEndFlag = "~}}" | "}}"
  slineEscapeValue = "{{" "~"? slineValueExpression<escapeValueEndFlag> "~"? "}}"

  rawValueEndFlag = "~}}}" | "}}}"
  slineRawValue  = "{{{" "~"? slineValueExpression<rawValueEndFlag> "~"? "}}}"
  
  tagEndFlag = space* ("/~}}" | "~}}" | "/}}" | "}}")
  tagStatement = (stringLiteral | anyExceptPlus<(tagEndFlag | slineQuotes)>)*
  slineSelfCloseTag = "{{" "~"? "#" identifier (space+ tagStatement)? space* "/" "~"? "}}"
  slineBlockTagOpen = "{{" "~"? "#" identifier (space+ tagStatement)? space* "~"? "}}"
  slineBlockTagClose = "{{" "~"? "/" identifier space* "~"? "}}"

  commentEndFlag = "--~}}" | "--}}"
  slineComment = "{{" "~"? "!--" anyExceptPlus<commentEndFlag> "--" "~"? "}}"
}

SlineHTML <: Sline {
  node := (htmlNode | slineNode | textNode)*
  
  openControl += "<"

  htmlNode =
    | htmlDoctype
    | htmlComment
    | htmlVoidElement
    | htmlSelfClosingElement
    | htmlTagClose
    | htmlTagOpen

  // https://html.spec.whatwg.org/multipage/syntax.html#the-doctype
  htmlDoctype =
    #("<!" caseInsensitive<"doctype"> space+ caseInsensitive<"html">) legacyDoctypeString? ">"
  legacyDoctypeString
    = anyExceptPlus<">">

  htmlComment = "<!--" #(anyExceptStar<"-->"> "-->")

  htmlVoidElement =
    #("<" voidElementName &(space | "/" | ">")) attrList "/"? ">"

  htmlSelfClosingElement =
    #("<" tagName) attrList "/>"

  htmlTagOpen =
    #("<" tagName) space* attrList space* ">"

  htmlTagClose =
    #("</" tagName) ">"

  tagName = leadingTagNamePart trailingTagNamePart*

  // The difference here is that the first text part must start
  // with a letter, but trailing text parts don't have that
  // requirement
  leadingTagNamePart =
    | slineValue
    | leadingTagNameTextNode

  trailingTagNamePart =
    | slineValue
    | trailingTagNameTextNode

  leadingTagNameTextNode = letter (alnum | "-" | ":")*
  trailingTagNameTextNode = (alnum | "-" | ":")+

  attrList = (space* attr space*)*

  attr =
    attrSingleQuoted | attrDoubleQuoted | attrUnquoted | slineNode | attrEmpty

  attrEmpty = attrName

  attrUnquoted = attrName "=" attrUnquotedValue
  attrSingleQuoted = attrName "=" singleQuote #(attrSingleQuotedValue singleQuote)
  attrDoubleQuoted = attrName "=" doubleQuote #(attrDoubleQuotedValue doubleQuote)

  attrName = (slineValue | attrNameTextNode)+

  // https://html.spec.whatwg.org/#attributes-2
  attrNameTextNode = anyExceptPlus<(space | quotes | "=" | ">" | "/>" | "{{" | controls | noncharacters)>
  attrUnquotedValue = (slineValue | attrUnquotedTextNode)*
  attrSingleQuotedValue = (slineNode | attrSingleQuotedTextNode)*
  attrDoubleQuotedValue = (slineNode | attrDoubleQuotedTextNode)*

  attrUnquotedTextNode = anyExceptPlus<(space | quotes | "=" | "<" | ">" | "`" | "{{")>
  attrSingleQuotedTextNode = anyExceptPlus<(singleQuote | "{{")>
  attrDoubleQuotedTextNode = anyExceptPlus<(doubleQuote | "{{")>

  quotes = singleQuote | doubleQuote

  identifierCharacter = alnum | "_" | "-"

  // https://www.w3.org/TR/2011/WD-html-markup-20110113/syntax.html#void-element
  voidElementName =
    ( caseInsensitive<"area">
    | caseInsensitive<"base">
    | caseInsensitive<"br">
    | caseInsensitive<"col">
    | caseInsensitive<"command">
    | caseInsensitive<"embed">
    | caseInsensitive<"hr">
    | caseInsensitive<"img">
    | caseInsensitive<"input">
    | caseInsensitive<"keygen">
    | caseInsensitive<"link">
    | caseInsensitive<"meta">
    | caseInsensitive<"param">
    | caseInsensitive<"source">
    | caseInsensitive<"track">
    | caseInsensitive<"wbr">
    ) ~identifierCharacter
}