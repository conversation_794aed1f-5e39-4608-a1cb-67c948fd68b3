"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toSlineAST = exports.SlineMappings = void 0;
const extras_1 = require("ohm-js/extras");
const common_1 = require("./common");
const types_1 = require("./types");
const expression_1 = require("./expression");
const tag_1 = require("../tag");
const statement_1 = require("./statement");
function buildSlineValue(node) {
    const offset = node.source.startIdx;
    const statement = node.sourceString;
    if (statement.length === 0) {
        return {
            error: {
                message: "empty expression",
                start: offset,
                end: offset,
            },
        };
    }
    const result = {};
    try {
        const parser = new statement_1.StatementParser(statement, offset);
        parser.readSpace(true);
        const expression = parser.readExpr();
        Object.assign(result, expression);
        parser.readSpace(true);
        parser.checkEnd();
    }
    catch (err) {
        if (err instanceof statement_1.StatementParseError) {
            result.error = {
                message: err.message,
                start: err.offset,
                end: offset + statement.length,
            };
        }
        else {
            throw err;
        }
    }
    return result;
}
const SlineMappings = (ctx) => ({
    ...(0, expression_1.SlineExpressionMappings)(ctx),
    slineNode: 0,
    slineValue: 0,
    slineEscapeValue: {
        type: types_1.NodeTypes.SlineValue,
        value: (tokens) => buildSlineValue(tokens[2]),
        escape: true,
        strip: (tokens) => ({
            start: tokens[1].sourceString,
            end: tokens[3].sourceString,
        }),
        loc: ctx.loc,
    },
    slineRawValue: {
        type: types_1.NodeTypes.SlineValue,
        value: (tokens) => buildSlineValue(tokens[2]),
        escape: false,
        strip: (tokens) => ({
            start: tokens[1].sourceString,
            end: tokens[3].sourceString,
        }),
        loc: ctx.loc,
    },
    slineSelfCloseTag: {
        type: types_1.NodeTypes.SlineSelfCloseTag,
        name: (tokens) => tokens[3].sourceString,
        statement: (tokens) => {
            var _a, _b, _c, _d, _e;
            const tagName = tokens[3].sourceString;
            const statement = (_c = (_b = (_a = tokens[5].children) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.sourceString) !== null && _c !== void 0 ? _c : "";
            const source = ((_e = (_d = tokens[5].children) === null || _d === void 0 ? void 0 : _d[0]) !== null && _e !== void 0 ? _e : tokens[5]).source;
            const parsedStat = (0, tag_1.parseTagStatement)(tagName, statement, source.startIdx);
            return parsedStat;
        },
        strip: (tokens) => ({
            start: tokens[1].sourceString,
            end: tokens[8].sourceString,
        }),
        loc: ctx.loc,
    },
    slineBlockTagOpen: {
        type: types_1.NodeTypes.SlineBlockTagOpen,
        name: (tokens) => tokens[3].sourceString,
        statement: (tokens) => {
            var _a, _b, _c, _d, _e;
            const tagName = tokens[3].sourceString;
            const statement = (_c = (_b = (_a = tokens[5].children) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.sourceString) !== null && _c !== void 0 ? _c : "";
            const source = ((_e = (_d = tokens[5].children) === null || _d === void 0 ? void 0 : _d[0]) !== null && _e !== void 0 ? _e : tokens[5]).source;
            const parsedStat = (0, tag_1.parseTagStatement)(tagName, statement, source.startIdx);
            return parsedStat;
        },
        strip: (tokens) => ({
            start: tokens[1].sourceString,
            end: tokens[7].sourceString,
        }),
        loc: ctx.loc,
    },
    slineBlockTagClose: {
        type: types_1.NodeTypes.SlineBlockTagClose,
        name: (tokens) => tokens[3].sourceString,
        strip: (tokens) => ({
            start: tokens[1].sourceString,
            end: tokens[5].sourceString,
        }),
        loc: ctx.loc,
    },
    slineComment: {
        type: types_1.NodeTypes.SlineComment,
        content: 3,
        strip: (tokens) => ({
            start: tokens[1].sourceString,
            end: tokens[5].sourceString,
        }),
        loc: ctx.loc,
    },
});
exports.SlineMappings = SlineMappings;
function toSlineAST(source) {
    const res = common_1.Grammars.Sline.match(source, "node");
    if (res.failed()) {
        throw new common_1.ParseError(res);
    }
    const ctx = (0, common_1.buildParseContext)(0);
    return (0, extras_1.toAST)(res, (0, exports.SlineMappings)(ctx));
}
exports.toSlineAST = toSlineAST;
//# sourceMappingURL=data:application/json;base64,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