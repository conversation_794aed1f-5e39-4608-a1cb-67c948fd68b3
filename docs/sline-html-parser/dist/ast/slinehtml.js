"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toSlineHtmlAST = exports.SlineHTMLMappings = void 0;
const common_1 = require("./common");
const types_1 = require("./types");
const sline_1 = require("./sline");
const extras_1 = require("ohm-js/extras");
const SlineHTMLMappings = (ctx) => ({
    ...(0, sline_1.SlineMappings)(ctx),
    htmlNode: 0,
    htmlDoctype: {
        type: types_1.NodeTypes.HtmlDoctype,
        legacyDoctypeString: 4,
        loc: ctx.loc,
    },
    htmlComment: {
        type: types_1.NodeTypes.HtmlComment,
        body: (0, common_1.markup)(1),
        loc: ctx.loc,
    },
    htmlVoidElement: {
        type: types_1.NodeTypes.HtmlVoidElement,
        name: 1,
        attrList: 3,
        loc: ctx.loc,
    },
    htmlSelfClosingElement: {
        type: types_1.NodeTypes.HtmlSelfClosingElement,
        name: 1,
        attrList: 2,
        loc: ctx.loc,
    },
    htmlTagOpen: {
        type: types_1.NodeTypes.HtmlTagOpen,
        name: 1,
        attrList: 3,
        loc: ctx.loc,
    },
    htmlTagClose: {
        type: types_1.NodeTypes.HtmlTagClose,
        name: 1,
        loc: ctx.loc,
    },
    leadingTagNamePart: 0,
    leadingTagNameTextNode: ctx.textNode,
    trailingTagNamePart: 0,
    trailingTagNameTextNode: ctx.textNode,
    tagName(leadingPart, trailingParts) {
        const mappings = this.args.mapping;
        return [leadingPart.toAST(mappings)].concat(trailingParts.toAST(mappings));
    },
    attr: 0,
    attrList: function (_space1, attr, _space2) {
        const mappings = this.args.mapping;
        return attr.children.map((child) => child.toAST(mappings));
    },
    attrUnquoted: {
        type: types_1.NodeTypes.AttrUnquoted,
        name: 0,
        value: 2,
        loc: ctx.loc,
    },
    attrSingleQuoted: {
        type: types_1.NodeTypes.AttrSingleQuoted,
        name: 0,
        value: 3,
        loc: ctx.loc,
    },
    attrDoubleQuoted: {
        type: types_1.NodeTypes.AttrDoubleQuoted,
        name: 0,
        value: 3,
        loc: ctx.loc,
    },
    attrEmpty: {
        type: types_1.NodeTypes.AttrEmpty,
        name: 0,
        loc: ctx.loc,
    },
    attrName: 0,
    attrNameTextNode: ctx.textNode,
    attrDoubleQuotedValue: 0,
    attrSingleQuotedValue: 0,
    attrUnquotedValue: 0,
    attrDoubleQuotedTextNode: ctx.textNode,
    attrSingleQuotedTextNode: ctx.textNode,
    attrUnquotedTextNode: ctx.textNode,
});
exports.SlineHTMLMappings = SlineHTMLMappings;
function toSlineHtmlAST(source) {
    const res = common_1.Grammars.SlineHTML.match(source, "node");
    if (res.failed()) {
        throw new common_1.ParseError(res);
    }
    const ctx = (0, common_1.buildParseContext)(0);
    return (0, extras_1.toAST)(res, (0, exports.SlineHTMLMappings)(ctx));
}
exports.toSlineHtmlAST = toSlineHtmlAST;
//# sourceMappingURL=data:application/json;base64,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