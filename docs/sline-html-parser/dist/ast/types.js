"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NodeTypes = void 0;
var NodeTypes;
(function (NodeTypes) {
    NodeTypes["TextNode"] = "TextNode";
    NodeTypes["Identifier"] = "Identifier";
    NodeTypes["Value"] = "Value";
    NodeTypes["NilLiteral"] = "NilLiteral";
    NodeTypes["BoolLiteral"] = "BoolLiteral";
    NodeTypes["IntegerLiteral"] = "IntegerLiteral";
    NodeTypes["FloatLiteral"] = "FloatLiteral";
    NodeTypes["StringLiteral"] = "StringLiteral";
    NodeTypes["Lookup"] = "Lookup";
    NodeTypes["SlineFilter"] = "SlineFilter";
    NodeTypes["HashPair"] = "HashPair";
    NodeTypes["BracketExpression"] = "BracketExpression";
    NodeTypes["LogicalExpression"] = "LogicalExpression";
    NodeTypes["NotExpression"] = "NotExpression";
    NodeTypes["BinaryExpression"] = "BinaryExpression";
    NodeTypes["SlineValue"] = "SlineValue";
    NodeTypes["SlineSelfCloseTag"] = "SlineSelfCloseTag";
    NodeTypes["SlineBlockTagOpen"] = "SlineBlockTagOpen";
    NodeTypes["SlineBlockTagClose"] = "SlineBlockTagClose";
    NodeTypes["SlineComment"] = "SlineComment";
    NodeTypes["HtmlDoctype"] = "HtmlDoctype";
    NodeTypes["HtmlComment"] = "HtmlComment";
    NodeTypes["HtmlVoidElement"] = "HtmlVoidElement";
    NodeTypes["HtmlSelfClosingElement"] = "HtmlSelfClosingElement";
    NodeTypes["HtmlTagOpen"] = "HtmlTagOpen";
    NodeTypes["HtmlTagClose"] = "HtmlTagClose";
    NodeTypes["AttrSingleQuoted"] = "AttrSingleQuoted";
    NodeTypes["AttrDoubleQuoted"] = "AttrDoubleQuoted";
    NodeTypes["AttrUnquoted"] = "AttrUnquoted";
    NodeTypes["AttrEmpty"] = "AttrEmpty";
})(NodeTypes = exports.NodeTypes || (exports.NodeTypes = {}));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidHlwZXMuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi9zcmMvYXN0L3R5cGVzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7OztBQUVBLElBQVksU0FpQ1g7QUFqQ0QsV0FBWSxTQUFTO0lBQ25CLGtDQUFxQixDQUFBO0lBQ3JCLHNDQUF5QixDQUFBO0lBRXpCLDRCQUFlLENBQUE7SUFDZixzQ0FBeUIsQ0FBQTtJQUN6Qix3Q0FBMkIsQ0FBQTtJQUMzQiw4Q0FBaUMsQ0FBQTtJQUNqQywwQ0FBNkIsQ0FBQTtJQUM3Qiw0Q0FBK0IsQ0FBQTtJQUMvQiw4QkFBaUIsQ0FBQTtJQUNqQix3Q0FBMkIsQ0FBQTtJQUMzQixrQ0FBcUIsQ0FBQTtJQUNyQixvREFBdUMsQ0FBQTtJQUN2QyxvREFBdUMsQ0FBQTtJQUN2Qyw0Q0FBK0IsQ0FBQTtJQUMvQixrREFBcUMsQ0FBQTtJQUNyQyxzQ0FBeUIsQ0FBQTtJQUN6QixvREFBdUMsQ0FBQTtJQUN2QyxvREFBdUMsQ0FBQTtJQUN2QyxzREFBeUMsQ0FBQTtJQUN6QywwQ0FBNkIsQ0FBQTtJQUU3Qix3Q0FBMkIsQ0FBQTtJQUMzQix3Q0FBMkIsQ0FBQTtJQUMzQixnREFBbUMsQ0FBQTtJQUNuQyw4REFBaUQsQ0FBQTtJQUNqRCx3Q0FBMkIsQ0FBQTtJQUMzQiwwQ0FBNkIsQ0FBQTtJQUM3QixrREFBcUMsQ0FBQTtJQUNyQyxrREFBcUMsQ0FBQTtJQUNyQywwQ0FBNkIsQ0FBQTtJQUM3QixvQ0FBdUIsQ0FBQTtBQUN6QixDQUFDLEVBakNXLFNBQVMsR0FBVCxpQkFBUyxLQUFULGlCQUFTLFFBaUNwQiJ9