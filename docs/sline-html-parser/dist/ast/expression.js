"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseExpressionLeft = exports.parseStrictExpression = exports.SlineExpressionMappings = void 0;
const extras_1 = require("ohm-js/extras");
const common_1 = require("./common");
const types_1 = require("./types");
const SlineExpressionMappings = (ctx) => ({
    ...(0, common_1.CommonMappings)(ctx),
    expression: 0,
    expressionWithRest: {
        expression: 1,
        rest: (tokens) => tokens[2].sourceString,
    },
    bracketExpression: {
        type: types_1.NodeTypes.BracketExpression,
        expression: 2,
        loc: ctx.loc,
    },
    logicalExpression: {
        type: types_1.NodeTypes.LogicalExpression,
        left: 0,
        operator: 2,
        right: 4,
        loc: ctx.loc,
    },
    notExpression: {
        type: types_1.NodeTypes.NotExpression,
        value: 1,
        loc: ctx.loc,
    },
    binaryOperand: 0,
    binaryExpression: {
        type: types_1.NodeTypes.BinaryExpression,
        left: 0,
        operator: 2,
        right: 4,
        loc: ctx.loc,
    },
    value: {
        type: types_1.NodeTypes.Value,
        operand: 0,
        filters: 1,
        loc: ctx.loc,
    },
    filter: {
        type: types_1.NodeTypes.SlineFilter,
        name: (tokens) => tokens[3].sourceString,
        args: 4,
        loc: ctx.loc,
    },
    filterArgs: 0,
    filterNoArgs: (_1, _2, _3) => [],
    filterPositionArgs: 2,
    filterHashArgs: 2,
    hashPair: {
        type: types_1.NodeTypes.HashPair,
        key: (tokens) => tokens[0].sourceString,
        value: 4,
        loc: ctx.loc,
    },
    filterPositionAndHashArgs(_l, _ls, posArgs, _posSep, hashArgs, _rs, _r) {
        const mapping = this.args.mapping;
        return posArgs.children
            .map((child) => child.toAST(mapping))
            .concat(hashArgs.toAST(mapping));
    },
    operand: 0,
    literal: 0,
    nilLiteral: {
        type: types_1.NodeTypes.NilLiteral,
        loc: ctx.loc,
    },
    boolLiteral: {
        type: types_1.NodeTypes.BoolLiteral,
        value: function () {
            return this.sourceString == "true";
        },
        loc: ctx.loc,
    },
    integerLiteral: {
        type: types_1.NodeTypes.IntegerLiteral,
        value: common_1.raw,
        loc: ctx.loc,
    },
    floatLiteral: {
        type: types_1.NodeTypes.FloatLiteral,
        value: common_1.raw,
        loc: ctx.loc,
    },
    stringLiteral: {
        type: types_1.NodeTypes.StringLiteral,
        value: (tokens) => tokens[1].sourceString,
        loc: ctx.loc,
    },
    lookup: {
        type: types_1.NodeTypes.Lookup,
        parts: function (tokens) {
            const mapping = this.args.mapping;
            return [tokens[0].toAST(mapping)].concat(tokens[1].toAST(mapping));
        },
        loc: ctx.loc,
    },
    dotLookup: 1,
    indexLookup: 1,
});
exports.SlineExpressionMappings = SlineExpressionMappings;
function parseStrictExpression(source, offset = 0) {
    const res = common_1.Grammars.Expression.match(source, "expression");
    if (res.failed()) {
        throw new common_1.ParseError(res);
    }
    const ctx = (0, common_1.buildParseContext)(offset);
    return (0, extras_1.toAST)(res, (0, exports.SlineExpressionMappings)(ctx));
}
exports.parseStrictExpression = parseStrictExpression;
function parseExpressionLeft(source, offset = 0) {
    var _a, _b;
    const res = common_1.Grammars.Expression.match(source, "expressionWithRest");
    if (res.failed()) {
        throw new common_1.ParseError(res);
    }
    const ctx = (0, common_1.buildParseContext)(offset);
    const ast = (0, extras_1.toAST)(res, (0, exports.SlineExpressionMappings)(ctx));
    return {
        expression: ast.expression,
        rest: ast.rest,
        offset: (_b = (_a = ast.expression) === null || _a === void 0 ? void 0 : _a.loc.end) !== null && _b !== void 0 ? _b : offset,
    };
}
exports.parseExpressionLeft = parseExpressionLeft;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZXhwcmVzc2lvbi5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy9hc3QvZXhwcmVzc2lvbi50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFDQSwwQ0FBc0M7QUFDdEMscUNBT2tCO0FBQ2xCLG1DQUF5QztBQUVsQyxNQUFNLHVCQUF1QixHQUFtQixDQUFDLEdBQUcsRUFBRSxFQUFFLENBQUMsQ0FBQztJQUMvRCxHQUFHLElBQUEsdUJBQWMsRUFBQyxHQUFHLENBQUM7SUFFdEIsVUFBVSxFQUFFLENBQUM7SUFFYixrQkFBa0IsRUFBRTtRQUNsQixVQUFVLEVBQUUsQ0FBQztRQUNiLElBQUksRUFBRSxDQUFDLE1BQWMsRUFBRSxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLFlBQVk7S0FDMUM7SUFFUixpQkFBaUIsRUFBRTtRQUNqQixJQUFJLEVBQUUsaUJBQVMsQ0FBQyxpQkFBaUI7UUFDakMsVUFBVSxFQUFFLENBQUM7UUFDYixHQUFHLEVBQUUsR0FBRyxDQUFDLEdBQUc7S0FDYjtJQUNELGlCQUFpQixFQUFFO1FBQ2pCLElBQUksRUFBRSxpQkFBUyxDQUFDLGlCQUFpQjtRQUNqQyxJQUFJLEVBQUUsQ0FBQztRQUNQLFFBQVEsRUFBRSxDQUFDO1FBQ1gsS0FBSyxFQUFFLENBQUM7UUFDUixHQUFHLEVBQUUsR0FBRyxDQUFDLEdBQUc7S0FDYjtJQUNELGFBQWEsRUFBRTtRQUNiLElBQUksRUFBRSxpQkFBUyxDQUFDLGFBQWE7UUFDN0IsS0FBSyxFQUFFLENBQUM7UUFDUixHQUFHLEVBQUUsR0FBRyxDQUFDLEdBQUc7S0FDYjtJQUNELGFBQWEsRUFBRSxDQUFDO0lBQ2hCLGdCQUFnQixFQUFFO1FBQ2hCLElBQUksRUFBRSxpQkFBUyxDQUFDLGdCQUFnQjtRQUNoQyxJQUFJLEVBQUUsQ0FBQztRQUNQLFFBQVEsRUFBRSxDQUFDO1FBQ1gsS0FBSyxFQUFFLENBQUM7UUFDUixHQUFHLEVBQUUsR0FBRyxDQUFDLEdBQUc7S0FDYjtJQUVELEtBQUssRUFBRTtRQUNMLElBQUksRUFBRSxpQkFBUyxDQUFDLEtBQUs7UUFDckIsT0FBTyxFQUFFLENBQUM7UUFDVixPQUFPLEVBQUUsQ0FBQztRQUNWLEdBQUcsRUFBRSxHQUFHLENBQUMsR0FBRztLQUNiO0lBQ0QsTUFBTSxFQUFFO1FBQ04sSUFBSSxFQUFFLGlCQUFTLENBQUMsV0FBVztRQUMzQixJQUFJLEVBQUUsQ0FBQyxNQUFjLEVBQUUsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxZQUFZO1FBQ2hELElBQUksRUFBRSxDQUFDO1FBQ1AsR0FBRyxFQUFFLEdBQUcsQ0FBQyxHQUFHO0tBQ2I7SUFDRCxVQUFVLEVBQUUsQ0FBQztJQUNiLFlBQVksRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsQ0FBQyxFQUFFO0lBQ2hDLGtCQUFrQixFQUFFLENBQUM7SUFDckIsY0FBYyxFQUFFLENBQUM7SUFDakIsUUFBUSxFQUFFO1FBQ1IsSUFBSSxFQUFFLGlCQUFTLENBQUMsUUFBUTtRQUN4QixHQUFHLEVBQUUsQ0FBQyxNQUFjLEVBQUUsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxZQUFZO1FBQy9DLEtBQUssRUFBRSxDQUFDO1FBQ1IsR0FBRyxFQUFFLEdBQUcsQ0FBQyxHQUFHO0tBQ2I7SUFDRCx5QkFBeUIsQ0FBQyxFQUFFLEVBQUUsR0FBRyxFQUFFLE9BQWEsRUFBRSxPQUFPLEVBQUUsUUFBYyxFQUFFLEdBQUcsRUFBRSxFQUFFO1FBQ2hGLE1BQU0sT0FBTyxHQUFJLElBQVksQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDO1FBQzNDLE9BQU8sT0FBTyxDQUFDLFFBQVE7YUFDcEIsR0FBRyxDQUFDLENBQUMsS0FBSyxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFDO2FBQ3BDLE1BQU0sQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUM7SUFDckMsQ0FBQztJQUVELE9BQU8sRUFBRSxDQUFDO0lBRVYsT0FBTyxFQUFFLENBQUM7SUFDVixVQUFVLEVBQUU7UUFDVixJQUFJLEVBQUUsaUJBQVMsQ0FBQyxVQUFVO1FBQzFCLEdBQUcsRUFBRSxHQUFHLENBQUMsR0FBRztLQUNiO0lBQ0QsV0FBVyxFQUFFO1FBQ1gsSUFBSSxFQUFFLGlCQUFTLENBQUMsV0FBVztRQUMzQixLQUFLLEVBQUU7WUFDTCxPQUFPLElBQUksQ0FBQyxZQUFZLElBQUksTUFBTSxDQUFDO1FBQ3JDLENBQUM7UUFDRCxHQUFHLEVBQUUsR0FBRyxDQUFDLEdBQUc7S0FDYjtJQUNELGNBQWMsRUFBRTtRQUNkLElBQUksRUFBRSxpQkFBUyxDQUFDLGNBQWM7UUFDOUIsS0FBSyxFQUFFLFlBQUc7UUFDVixHQUFHLEVBQUUsR0FBRyxDQUFDLEdBQUc7S0FDYjtJQUNELFlBQVksRUFBRTtRQUNaLElBQUksRUFBRSxpQkFBUyxDQUFDLFlBQVk7UUFDNUIsS0FBSyxFQUFFLFlBQUc7UUFDVixHQUFHLEVBQUUsR0FBRyxDQUFDLEdBQUc7S0FDYjtJQUNELGFBQWEsRUFBRTtRQUNiLElBQUksRUFBRSxpQkFBUyxDQUFDLGFBQWE7UUFDN0IsS0FBSyxFQUFFLENBQUMsTUFBYyxFQUFFLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsWUFBWTtRQUNqRCxHQUFHLEVBQUUsR0FBRyxDQUFDLEdBQUc7S0FDYjtJQUVELE1BQU0sRUFBRTtRQUNOLElBQUksRUFBRSxpQkFBUyxDQUFDLE1BQU07UUFDdEIsS0FBSyxFQUFFLFVBQXFCLE1BQWM7WUFDeEMsTUFBTSxPQUFPLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUM7WUFDbEMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDO1FBQ3JFLENBQUM7UUFDRCxHQUFHLEVBQUUsR0FBRyxDQUFDLEdBQUc7S0FDYjtJQUNELFNBQVMsRUFBRSxDQUFDO0lBQ1osV0FBVyxFQUFFLENBQUM7Q0FDZixDQUFDLENBQUM7QUF6R1UsUUFBQSx1QkFBdUIsMkJBeUdqQztBQUVILFNBQWdCLHFCQUFxQixDQUNuQyxNQUFjLEVBQ2QsTUFBTSxHQUFHLENBQUM7SUFFVixNQUFNLEdBQUcsR0FBRyxpQkFBUSxDQUFDLFVBQVUsQ0FBQyxLQUFLLENBQUMsTUFBTSxFQUFFLFlBQVksQ0FBQyxDQUFDO0lBQzVELElBQUksR0FBRyxDQUFDLE1BQU0sRUFBRSxFQUFFO1FBQ2hCLE1BQU0sSUFBSSxtQkFBVSxDQUFDLEdBQUcsQ0FBQyxDQUFDO0tBQzNCO0lBQ0QsTUFBTSxHQUFHLEdBQUcsSUFBQSwwQkFBaUIsRUFBQyxNQUFNLENBQUMsQ0FBQztJQUN0QyxPQUFPLElBQUEsY0FBSyxFQUFDLEdBQUcsRUFBRSxJQUFBLCtCQUF1QixFQUFDLEdBQUcsQ0FBQyxDQUFtQixDQUFDO0FBQ3BFLENBQUM7QUFWRCxzREFVQztBQUVELFNBQWdCLG1CQUFtQixDQUNqQyxNQUFjLEVBQ2QsTUFBTSxHQUFHLENBQUM7O0lBTVYsTUFBTSxHQUFHLEdBQUcsaUJBQVEsQ0FBQyxVQUFVLENBQUMsS0FBSyxDQUFDLE1BQU0sRUFBRSxvQkFBb0IsQ0FBQyxDQUFDO0lBQ3BFLElBQUksR0FBRyxDQUFDLE1BQU0sRUFBRSxFQUFFO1FBQ2hCLE1BQU0sSUFBSSxtQkFBVSxDQUFDLEdBQUcsQ0FBQyxDQUFDO0tBQzNCO0lBQ0QsTUFBTSxHQUFHLEdBQUcsSUFBQSwwQkFBaUIsRUFBQyxNQUFNLENBQUMsQ0FBQztJQUN0QyxNQUFNLEdBQUcsR0FBRyxJQUFBLGNBQUssRUFBQyxHQUFHLEVBQUUsSUFBQSwrQkFBdUIsRUFBQyxHQUFHLENBQUMsQ0FBUSxDQUFDO0lBQzVELE9BQU87UUFDTCxVQUFVLEVBQUUsR0FBRyxDQUFDLFVBQVU7UUFDMUIsSUFBSSxFQUFFLEdBQUcsQ0FBQyxJQUFJO1FBQ2QsTUFBTSxFQUFFLE1BQUEsTUFBQSxHQUFHLENBQUMsVUFBVSwwQ0FBRSxHQUFHLENBQUMsR0FBRyxtQ0FBSSxNQUFNO0tBQzFDLENBQUM7QUFDSixDQUFDO0FBbkJELGtEQW1CQyJ9