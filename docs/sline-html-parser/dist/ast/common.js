"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParseError = exports.CommonMappings = exports.buildParseContext = exports.markup = exports.raw = exports.Grammars = exports.slineHtmlGrammars = void 0;
const ohm_js_1 = __importDefault(require("ohm-js"));
const types_1 = require("./types");
exports.slineHtmlGrammars = ohm_js_1.default.grammars(require("../../grammar/sline-html.ohm.js"));
exports.Grammars = {
    Expression: exports.slineHtmlGrammars["Expression"],
    Sline: exports.slineHtmlGrammars["Sline"],
    SlineHTML: exports.slineHtmlGrammars["SlineHTML"],
};
const raw = function () {
    return this.sourceString;
};
exports.raw = raw;
const markup = (i) => (tokens) => tokens[i].sourceString.trim();
exports.markup = markup;
function buildParseContext(offset = 0) {
    const loc = function (tokens) {
        return {
            source: this.sourceString,
            start: offset + tokens[0].source.startIdx,
            end: offset + tokens[tokens.length - 1].source.endIdx,
        };
    };
    const textNode = {
        type: types_1.NodeTypes.TextNode,
        loc,
        raw: exports.raw,
    };
    return { offset, loc, textNode };
}
exports.buildParseContext = buildParseContext;
const CommonMappings = (ctx) => ({
    node: 0,
    textNode: ctx.textNode,
    nonemptyListOf(first, _sep, rest) {
        const mapping = this.args.mapping;
        return [first.toAST(mapping)].concat(rest.toAST(mapping));
    },
    identifier: {
        type: types_1.NodeTypes.Identifier,
        key: exports.raw,
        loc: ctx.loc,
    },
});
exports.CommonMappings = CommonMappings;
class ParseError extends Error {
    constructor(message) {
        super(message.shortMessage);
        this.detailMessage = message.message;
    }
}
exports.ParseError = ParseError;
//# sourceMappingURL=data:application/json;base64,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