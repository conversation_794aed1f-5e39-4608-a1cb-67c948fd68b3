"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatementParser = exports.StatementParseError = void 0;
const expression_1 = require("./expression");
class StatementParseError extends Error {
    constructor(message, offset) {
        super(message);
        this.offset = offset;
    }
}
exports.StatementParseError = StatementParseError;
class StatementParser {
    constructor(statement, startOffset) {
        this.statement = statement;
        this.startOffset = startOffset;
        this.offset = startOffset;
    }
    get rest() {
        return this.statement.slice(this.offset - this.startOffset);
    }
    readRegexp(reg) {
        const res = this.rest.match(reg);
        if (!res)
            return null;
        if (res.index != 0)
            return null;
        const value = res[0];
        this.offset += value.length;
        return value;
    }
    readString(str) {
        if (!this.rest.startsWith(str)) {
            this.throwError(`expect '${str}'`);
        }
        this.offset += str.length;
    }
    startsWith(str) {
        return this.rest.startsWith(str);
    }
    throwError(msg) {
        throw new StatementParseError(msg, this.offset);
    }
    get end() {
        return this.offset === this.startOffset + this.statement.length;
    }
    checkEnd() {
        if (!this.end) {
            this.throwError("invalid statement");
        }
    }
    readSpace(allowEmpty = true) {
        if (allowEmpty) {
            this.readRegexp(/^\s*/);
        }
        else {
            if (this.end)
                return;
            const r = this.readRegexp(/^\s+/);
            if (r == null) {
                this.throwError("expect space");
            }
        }
    }
    readKey(allowHyphen = false) {
        const key = allowHyphen
            ? this.readRegexp(/^[a-zA-Z_][\w-]*/)
            : this.readRegexp(/^[a-zA-Z_]\w*/);
        if (key == null) {
            this.throwError("expect key");
        }
        return key;
    }
    readEqualSymbol() {
        const key = this.readRegexp(/^\s*=\s*/);
        if (key == null) {
            this.throwError("expect '='");
        }
    }
    readExpr() {
        const res = (0, expression_1.parseExpressionLeft)(this.rest, this.offset);
        if (res.offset === this.offset) {
            this.throwError("empty expression");
        }
        this.offset = res.offset;
        return res.expression;
    }
}
exports.StatementParser = StatementParser;
//# sourceMappingURL=data:application/json;base64,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