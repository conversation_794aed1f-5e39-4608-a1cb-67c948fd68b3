"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TAG_RULES = void 0;
const types_1 = require("./types");
exports.TAG_RULES = {
    set: {
        type: types_1.TagType.SelfClose,
        parse(parser) {
            const key = parser.readKey();
            parser.readEqualSymbol();
            const value = parser.readExpr();
            return { key, value };
        },
    },
    var: {
        type: types_1.TagType.SelfClose,
        parse(parser) {
            const key = parser.readKey();
            parser.readSpace();
            if (parser.end) {
                return { key, value: null };
            }
            parser.readEqualSymbol();
            const value = parser.readExpr();
            return { key, value };
        },
    },
    if: {
        type: types_1.TagType.Block,
        parse(parser) {
            return { condition: parser.readExpr() };
        },
    },
    else: {
        type: types_1.TagType.SelfClose,
        parse(parser) {
            if (!parser.startsWith("if")) {
                return { if: false };
            }
            parser.readString("if");
            parser.readSpace(false);
            return {
                if: true,
                condition: parser.readExpr(),
            };
        },
    },
    switch: {
        type: types_1.TagType.Block,
        parse(parser) {
            return { data: parser.readExpr() };
        },
    },
    case: {
        type: types_1.TagType.SelfClose,
        parse(parser) {
            const values = [];
            while (!parser.end) {
                if (values.length > 0) {
                    parser.readSpace(false);
                }
                values.push(parser.readExpr());
            }
            return { values };
        },
    },
    for: {
        type: types_1.TagType.Block,
        parse(parser) {
            const key = parser.readKey();
            parser.readSpace(false);
            parser.readString("in");
            parser.readSpace(false);
            const data = parser.readExpr();
            return { itemKey: key, data };
        },
    },
    capture: {
        type: types_1.TagType.Block,
        parse(parser) {
            const key = parser.readKey(false);
            return { varName: key };
        },
    },
    break: {
        type: types_1.TagType.SelfClose,
        parse(parser) {
            return {};
        },
    },
    continue: {
        type: types_1.TagType.SelfClose,
        parse(parser) {
            return {};
        },
    },
    // theme
    content: defaultTagRule(types_1.TagType.SelfClose, 1, false),
    section: defaultTagRule(types_1.TagType.SelfClose, 1, false),
    sections: defaultTagRule(types_1.TagType.SelfClose, 1, false),
    block: defaultTagRule(types_1.TagType.SelfClose, 0, true),
    blocks: defaultTagRule(types_1.TagType.Block, 0, false),
    component: defaultTagRule(types_1.TagType.SelfClose, 1, true),
    layout: defaultTagRule(types_1.TagType.SelfClose, 1, false),
    // element
    placeholder_svg: defaultTagRule(types_1.TagType.SelfClose, 1, true),
    payment_type_svg: defaultTagRule(types_1.TagType.SelfClose, 1, true),
    style: defaultTagRule(types_1.TagType.Block, 0, true),
    link_to: defaultTagRule(types_1.TagType.Block, 1, true),
    highlight: defaultTagRule(types_1.TagType.Block, 1, true),
    preload: defaultTagRule(types_1.TagType.Block, 1, true),
    stylesheet: defaultTagRule(types_1.TagType.SelfClose, 1, true),
    script: defaultTagRule(types_1.TagType.Block, 1, true),
    format_address: defaultTagRule(types_1.TagType.SelfClose, 1, true),
    link_to_tag: defaultTagRule(types_1.TagType.Block, 1, true),
    link_to_add_tag: defaultTagRule(types_1.TagType.Block, 1, true),
    link_to_remove_tag: defaultTagRule(types_1.TagType.Block, 1, true),
    link_to_vendor: defaultTagRule(types_1.TagType.SelfClose, 1, true),
    link_to_type: defaultTagRule(types_1.TagType.SelfClose, 1, true),
    metafield_tag: defaultTagRule(types_1.TagType.SelfClose, 1, true),
    link_to_customer_login: defaultTagRule(types_1.TagType.SelfClose, 1, true),
    link_to_customer_logout: defaultTagRule(types_1.TagType.SelfClose, 1, true),
    link_to_customer_register: defaultTagRule(types_1.TagType.SelfClose, 1, true),
    image_tag: defaultTagRule(types_1.TagType.SelfClose, 1, true),
    external_video_tag: defaultTagRule(types_1.TagType.SelfClose, 1, true),
    video_tag: defaultTagRule(types_1.TagType.SelfClose, 1, true),
    payment_button: defaultTagRule(types_1.TagType.SelfClose, 0, false),
    time_tag: defaultTagRule(types_1.TagType.SelfClose, 1, true),
    product_item: defaultTagRule(types_1.TagType.Block, 1, true),
    // form
    activate_customer_password_form: formTagRule(0, true),
    cart_form: formTagRule(0, true),
    contact_form: formTagRule(0, true),
    create_customer_form: formTagRule(0, true),
    customer_form: formTagRule(0, true),
    customer_address_form: formTagRule(0, true),
    customer_login_form: formTagRule(0, true),
    localization_form: formTagRule(0, true),
    new_comment_form: formTagRule(1, true),
    product_form: formTagRule(1, true),
    reset_customer_password_form: formTagRule(0, true),
    storefront_password_form: formTagRule(0, true),
    delete_customer_form: formTagRule(0, true),
    update_customer_form: formTagRule(0, true),
    bind_customer_email_form: formTagRule(0, true),
    bind_customer_phone_form: formTagRule(0, true),
    order_tracking_form: formTagRule(0, true),
    customer_subscribe_form: formTagRule(0, true),
    customer_unsubscribe_form: formTagRule(0, true),
    cancel_delete_customer_form: formTagRule(0, true),
    company_account_application_form: formTagRule(0, true),
};
function defaultTagRule(type, positionArgsSize, allowHashArgs) {
    return {
        type,
        parse(parser) {
            const positionArgs = [];
            const hashArgs = [];
            if (positionArgsSize > 0) {
                for (let i = 0; i < positionArgsSize; i++) {
                    positionArgs.push(parser.readExpr());
                    if (i !== positionArgsSize - 1) {
                        parser.readSpace(false);
                    }
                }
            }
            if (!parser.end && allowHashArgs) {
                if (positionArgsSize > 0) {
                    parser.readSpace(false);
                }
                while (true) {
                    const key = parser.readKey(true);
                    parser.readEqualSymbol();
                    const value = parser.readExpr();
                    hashArgs.push({ key, value });
                    if (parser.end)
                        break;
                    parser.readSpace(false);
                }
            }
            return { positionArgs, hashArgs };
        },
    };
}
function formTagRule(positionArgsSize, allowHashArgs) {
    return defaultTagRule(types_1.TagType.Block, positionArgsSize, allowHashArgs);
}
//# sourceMappingURL=data:application/json;base64,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