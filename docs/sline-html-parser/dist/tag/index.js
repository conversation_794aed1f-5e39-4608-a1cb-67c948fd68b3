"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TagType = exports.isSelfCloseTag = exports.isBlockTag = exports.isUsableTag = exports.parseTagStatement = void 0;
const statement_1 = require("../ast/statement");
const tags_1 = require("./tags");
const types_1 = require("./types");
function parseTagStatement(name, statement, offset = 0) {
    const loc = {
        start: offset,
        end: offset + statement.length,
        source: statement,
    };
    const tagRule = tags_1.TAG_RULES[name];
    if (!tagRule)
        return { loc };
    const parser = new statement_1.StatementParser(statement, offset);
    const result = { loc };
    try {
        const extend = tagRule.parse(parser);
        Object.assign(result, extend);
        parser.checkEnd();
    }
    catch (e) {
        if (e instanceof statement_1.StatementParseError) {
            result.error = { message: e.message, start: e.offset, end: loc.end };
        }
        else {
            throw e;
        }
    }
    return result;
}
exports.parseTagStatement = parseTagStatement;
function tagType(name) {
    var _a;
    return (_a = tags_1.TAG_RULES[name]) === null || _a === void 0 ? void 0 : _a.type;
}
function isUsableTag(name) {
    return name in tags_1.TAG_RULES;
}
exports.isUsableTag = isUsableTag;
function isBlockTag(name) {
    return tagType(name) === types_1.TagType.Block;
}
exports.isBlockTag = isBlockTag;
function isSelfCloseTag(name) {
    return tagType(name) === types_1.TagType.SelfClose;
}
exports.isSelfCloseTag = isSelfCloseTag;
var types_2 = require("./types");
Object.defineProperty(exports, "TagType", { enumerable: true, get: function () { return types_2.TagType; } });
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi9zcmMvdGFnL2luZGV4LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7OztBQUNBLGdEQUF3RTtBQUN4RSxpQ0FBNEM7QUFDNUMsbUNBQW9FO0FBRXBFLFNBQWdCLGlCQUFpQixDQUMvQixJQUFZLEVBQ1osU0FBaUIsRUFDakIsTUFBTSxHQUFHLENBQUM7SUFFVixNQUFNLEdBQUcsR0FBdUI7UUFDOUIsS0FBSyxFQUFFLE1BQU07UUFDYixHQUFHLEVBQUUsTUFBTSxHQUFHLFNBQVMsQ0FBQyxNQUFNO1FBQzlCLE1BQU0sRUFBRSxTQUFTO0tBQ2xCLENBQUM7SUFDRixNQUFNLE9BQU8sR0FBRyxnQkFBUyxDQUFDLElBQUksQ0FBaUIsQ0FBQztJQUNoRCxJQUFJLENBQUMsT0FBTztRQUFFLE9BQU8sRUFBRSxHQUFHLEVBQUUsQ0FBQztJQUM3QixNQUFNLE1BQU0sR0FBRyxJQUFJLDJCQUFlLENBQUMsU0FBUyxFQUFFLE1BQU0sQ0FBQyxDQUFDO0lBQ3RELE1BQU0sTUFBTSxHQUEyQyxFQUFFLEdBQUcsRUFBRSxDQUFDO0lBQy9ELElBQUk7UUFDRixNQUFNLE1BQU0sR0FBRyxPQUFPLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQ3JDLE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBQzlCLE1BQU0sQ0FBQyxRQUFRLEVBQUUsQ0FBQztLQUNuQjtJQUFDLE9BQU8sQ0FBQyxFQUFFO1FBQ1YsSUFBSSxDQUFDLFlBQVksK0JBQW1CLEVBQUU7WUFDcEMsTUFBTSxDQUFDLEtBQUssR0FBRyxFQUFFLE9BQU8sRUFBRSxDQUFDLENBQUMsT0FBTyxFQUFFLEtBQUssRUFBRSxDQUFDLENBQUMsTUFBTSxFQUFFLEdBQUcsRUFBRSxHQUFHLENBQUMsR0FBRyxFQUFFLENBQUM7U0FDdEU7YUFBTTtZQUNMLE1BQU0sQ0FBQyxDQUFDO1NBQ1Q7S0FDRjtJQUNELE9BQU8sTUFBTSxDQUFDO0FBQ2hCLENBQUM7QUExQkQsOENBMEJDO0FBRUQsU0FBUyxPQUFPLENBQUMsSUFBWTs7SUFDM0IsT0FBTyxNQUFBLGdCQUFTLENBQUMsSUFBSSxDQUFDLDBDQUFFLElBQUksQ0FBQztBQUMvQixDQUFDO0FBRUQsU0FBZ0IsV0FBVyxDQUFDLElBQVk7SUFDdEMsT0FBTyxJQUFJLElBQUksZ0JBQVMsQ0FBQztBQUMzQixDQUFDO0FBRkQsa0NBRUM7QUFFRCxTQUFnQixVQUFVLENBQUMsSUFBWTtJQUNyQyxPQUFPLE9BQU8sQ0FBQyxJQUFJLENBQUMsS0FBSyxlQUFPLENBQUMsS0FBSyxDQUFDO0FBQ3pDLENBQUM7QUFGRCxnQ0FFQztBQUVELFNBQWdCLGNBQWMsQ0FBQyxJQUFZO0lBQ3pDLE9BQU8sT0FBTyxDQUFDLElBQUksQ0FBQyxLQUFLLGVBQU8sQ0FBQyxTQUFTLENBQUM7QUFDN0MsQ0FBQztBQUZELHdDQUVDO0FBRUQsaUNBQXNFO0FBQTlDLGdHQUFBLE9BQU8sT0FBQSJ9