"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorTypes = exports.checkASTError = void 0;
var check_1 = require("./check");
Object.defineProperty(exports, "checkASTError", { enumerable: true, get: function () { return check_1.checkASTError; } });
var types_1 = require("./types");
Object.defineProperty(exports, "ErrorTypes", { enumerable: true, get: function () { return types_1.ErrorTypes; } });
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi9zcmMvY2hlY2svaW5kZXgudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEsaUNBQXdDO0FBQS9CLHNHQUFBLGFBQWEsT0FBQTtBQUN0QixpQ0FBaUQ7QUFBeEMsbUdBQUEsVUFBVSxPQUFBIn0=