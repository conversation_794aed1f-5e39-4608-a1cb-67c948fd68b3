"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorTypes = void 0;
var ErrorTypes;
(function (ErrorTypes) {
    ErrorTypes["MissingOpenTag"] = "MissingOpenTag";
    ErrorTypes["UnclosedTag"] = "UnclosedTag";
    ErrorTypes["SelfCloseTagOnly"] = "SelfCloseTagOnly";
    ErrorTypes["BlockTagOnly"] = "BlockTagOnly";
    ErrorTypes["UnknownTag"] = "UnknownTag";
    ErrorTypes["InvalidTagStatement"] = "InvalidTagStatement";
    ErrorTypes["InvalidExpression"] = "InvalidExpression";
})(ErrorTypes = exports.ErrorTypes || (exports.ErrorTypes = {}));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidHlwZXMuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi9zcmMvY2hlY2svdHlwZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBRUEsSUFBWSxVQVFYO0FBUkQsV0FBWSxVQUFVO0lBQ3BCLCtDQUFpQyxDQUFBO0lBQ2pDLHlDQUEyQixDQUFBO0lBQzNCLG1EQUFxQyxDQUFBO0lBQ3JDLDJDQUE2QixDQUFBO0lBQzdCLHVDQUF5QixDQUFBO0lBQ3pCLHlEQUEyQyxDQUFBO0lBQzNDLHFEQUF1QyxDQUFBO0FBQ3pDLENBQUMsRUFSVyxVQUFVLEdBQVYsa0JBQVUsS0FBVixrQkFBVSxRQVFyQiJ9