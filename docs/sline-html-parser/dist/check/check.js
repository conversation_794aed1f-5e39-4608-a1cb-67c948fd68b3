"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkASTError = void 0;
const ast_1 = require("../ast");
const tag_1 = require("../tag");
const types_1 = require("./types");
function checkErrorInternal(node, ctx) {
    switch (node.type) {
        case ast_1.NodeTypes.SlineValue:
            if (node.value.error) {
                ctx.errors.push({
                    type: types_1.ErrorTypes.InvalidExpression,
                    loc: node.loc,
                    node,
                    error: node.value.error,
                });
            }
            break;
        case ast_1.NodeTypes.SlineSelfCloseTag:
            if (!(0, tag_1.isUsableTag)(node.name)) {
                ctx.errors.push({
                    type: types_1.ErrorTypes.UnknownTag,
                    tag: node.name,
                    loc: node.loc,
                });
                break;
            }
            if ((0, tag_1.isBlockTag)(node.name)) {
                ctx.errors.push({
                    type: types_1.ErrorTypes.BlockTagOnly,
                    tag: node.name,
                    loc: node.loc,
                });
                break;
            }
            if (node.statement.error) {
                ctx.errors.push({
                    type: types_1.ErrorTypes.InvalidTagStatement,
                    loc: node.statement.loc,
                    tag: node.name,
                    node,
                    error: node.statement.error,
                });
            }
            break;
        case ast_1.NodeTypes.SlineBlockTagOpen:
            if (!(0, tag_1.isUsableTag)(node.name)) {
                ctx.errors.push({
                    type: types_1.ErrorTypes.UnknownTag,
                    tag: node.name,
                    loc: node.loc,
                });
                break;
            }
            if ((0, tag_1.isSelfCloseTag)(node.name)) {
                ctx.errors.push({
                    type: types_1.ErrorTypes.SelfCloseTagOnly,
                    tag: node.name,
                    loc: node.loc,
                });
                break;
            }
            ctx.tagStack.push(node);
            if (node.statement.error) {
                ctx.errors.push({
                    type: types_1.ErrorTypes.InvalidTagStatement,
                    loc: node.loc,
                    tag: node.name,
                    node,
                    error: node.statement.error,
                });
            }
            break;
        case ast_1.NodeTypes.SlineBlockTagClose:
            if (!(0, tag_1.isUsableTag)(node.name)) {
                ctx.errors.push({
                    type: types_1.ErrorTypes.UnknownTag,
                    tag: node.name,
                    loc: node.loc,
                });
                break;
            }
            let skipNodes = [];
            let matched = false;
            let lastNode;
            while (ctx.tagStack.length > 0) {
                lastNode = ctx.tagStack.pop();
                if (node.name === lastNode.name) {
                    matched = true;
                    break;
                }
                skipNodes.push(lastNode);
            }
            if (matched) {
                // 如果有匹配的开标签, 则所有跳过的节点当做未闭合错误
                for (let skipNode of skipNodes) {
                    ctx.errors.push({
                        type: types_1.ErrorTypes.UnclosedTag,
                        tag: skipNode.name,
                        loc: skipNode.loc,
                    });
                }
            }
            else {
                // 如果没有匹配的开标签, 则当前标签为缺开标签错误, 且所有跳过的节点重新加入到tagStack中
                ctx.errors.push({
                    type: types_1.ErrorTypes.MissingOpenTag,
                    tag: node.name,
                    loc: node.loc,
                });
                while (skipNodes.length > 0) {
                    ctx.tagStack.push(skipNodes.pop());
                }
            }
            break;
        default:
            break;
    }
}
function checkASTError(ast) {
    const ctx = { tagStack: [], errors: [] };
    if (Array.isArray(ast)) {
        for (let node of ast) {
            checkErrorInternal(node, ctx);
        }
    }
    else {
        checkErrorInternal(ast, ctx);
    }
    for (let node of ctx.tagStack) {
        ctx.errors.push({
            type: types_1.ErrorTypes.UnclosedTag,
            tag: node.name,
            loc: node.loc,
        });
    }
    return ctx.errors;
}
exports.checkASTError = checkASTError;
//# sourceMappingURL=data:application/json;base64,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