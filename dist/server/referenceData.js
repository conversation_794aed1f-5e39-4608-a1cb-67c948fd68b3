"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReferenceDataManager = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const node_1 = require("vscode-languageserver/node");
class ReferenceDataManager {
    constructor(extensionPath) {
        this.extensionPath = extensionPath;
        this.tags = new Map();
        this.objects = new Map();
        this.filters = new Map();
        this.objectProperties = new Map();
        this.tagCompletionItems = [];
        this.filterCompletionItems = [];
        this.objectCompletionItems = [];
    }
    async loadReferenceData() {
        try {
            console.log('Loading SLine reference data...');
            const tagsData = await this.loadJsonFile('tag.json');
            const objectsData = await this.loadJsonFile('objects.json');
            const filtersData = await this.loadJsonFile('filter.json');
            this.buildTagsIndex(tagsData);
            this.buildObjectsIndex(objectsData);
            this.buildFiltersIndex(filtersData);
            this.supplementMissingTags();
            this.supplementMissingFilters();
            this.precomputeCompletionItems();
            console.log(`Loaded ${this.tags.size} tags, ${this.objects.size} objects, ${this.filters.size} filters`);
            if (this.tags.size === 0 && this.objects.size === 0 && this.filters.size === 0) {
                console.log('No reference data loaded, using fallback data...');
                this.loadFallbackData();
            }
        }
        catch (error) {
            console.error('Failed to load reference data:', error);
            console.log('Using fallback data due to loading error...');
            this.loadFallbackData();
        }
    }
    async loadJsonFile(filename) {
        const possiblePaths = [
            path.join(this.extensionPath, 'src', filename),
            path.join(this.extensionPath, filename),
            path.join(__dirname, '..', filename),
            path.join(__dirname, '..', 'src', filename),
            path.join(process.cwd(), 'src', filename)
        ];
        let filePath = null;
        for (const testPath of possiblePaths) {
            if (fs.existsSync(testPath)) {
                filePath = testPath;
                break;
            }
        }
        if (!filePath) {
            console.warn(`Reference file not found: ${filename}. Tried paths:`, possiblePaths);
            return [];
        }
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const data = JSON.parse(content);
            console.log(`Loaded ${filename} from ${filePath}: ${Array.isArray(data) ? data.length : 'unknown'} items`);
            return Array.isArray(data) ? data : [];
        }
        catch (error) {
            console.error(`Error loading ${filename}:`, error);
            return [];
        }
    }
    buildTagsIndex(tagsData) {
        for (const tag of tagsData) {
            this.tags.set(tag.name, tag);
        }
    }
    buildObjectsIndex(objectsData) {
        for (const obj of objectsData) {
            this.objects.set(obj.name, obj);
            this.objectProperties.set(obj.name, obj.properties);
        }
    }
    buildFiltersIndex(filtersData) {
        for (const filter of filtersData) {
            this.filters.set(filter.name, filter);
        }
    }
    precomputeCompletionItems() {
        this.tagCompletionItems = Array.from(this.tags.values()).map(tag => this.createTagCompletionItem(tag));
        this.filterCompletionItems = Array.from(this.filters.values()).map(filter => this.createFilterCompletionItem(filter));
        this.objectCompletionItems = Array.from(this.objects.values()).map(obj => this.createObjectCompletionItem(obj));
    }
    createTagCompletionItem(tag) {
        const hasArguments = tag.arguments && tag.arguments.length > 0;
        const hasHashs = tag.hashs && tag.hashs.length > 0;
        const isSelfClosingTag = this.isSelfClosingTag(tag.name);
        let insertText = tag.name;
        if (isSelfClosingTag) {
            if (hasArguments || hasHashs) {
                insertText += ' ';
            }
        }
        else {
            if (hasArguments || hasHashs) {
                insertText += ' ';
            }
        }
        return {
            label: tag.name,
            kind: node_1.CompletionItemKind.Function,
            detail: `Sline Tag - ${tag.summary}${isSelfClosingTag ? ' (自闭合)' : ''}`,
            documentation: {
                kind: node_1.MarkupKind.Markdown,
                value: this.formatTagDocumentation(tag)
            },
            insertText: insertText,
            data: { type: 'tag', name: tag.name },
            deprecated: tag.deprecated,
            sortText: tag.deprecated ? 'z' + tag.name : 'a' + tag.name
        };
    }
    isSelfClosingTag(tagName) {
        const selfClosingTags = [
            'layout', 'var', 'component', 'content', 'section', 'sections',
            'stylesheet', 'script', 'meta-tags', 'set', 'image_tag', 'external_video_tag',
            'video_tag', 'time_tag', 'metafield_tag', 'preload_tag', 'payment_button'
        ];
        return selfClosingTags.includes(tagName);
    }
    createFilterCompletionItem(filter) {
        const hasArguments = filter.arguments && filter.arguments.length > 0;
        let insertText = filter.name;
        if (hasArguments) {
            insertText += '($0)';
        }
        else {
            insertText += '()';
        }
        return {
            label: filter.name,
            kind: node_1.CompletionItemKind.Function,
            detail: `Sline Filter - ${filter.summary}`,
            documentation: {
                kind: node_1.MarkupKind.Markdown,
                value: this.formatFilterDocumentation(filter)
            },
            insertText: insertText,
            data: { type: 'filter', name: filter.name },
            deprecated: filter.deprecated,
            sortText: filter.deprecated ? 'z' + filter.name : 'a' + filter.name
        };
    }
    createObjectCompletionItem(obj) {
        return {
            label: obj.name,
            kind: node_1.CompletionItemKind.Class,
            detail: `Sline Object - ${obj.summary}`,
            documentation: {
                kind: node_1.MarkupKind.Markdown,
                value: this.formatObjectDocumentation(obj)
            },
            insertText: obj.name,
            data: { type: 'object', name: obj.name },
            deprecated: obj.deprecated,
            sortText: obj.deprecated ? 'z' + obj.name : 'a' + obj.name
        };
    }
    formatTagDocumentation(tag) {
        let doc = `# ${tag.name}\n\n${tag.summary}\n\n`;
        if (tag.syntax) {
            doc += `## Syntax\n\`\`\`sline\n${tag.syntax}\n\`\`\`\n\n`;
        }
        if (tag.arguments && tag.arguments.length > 0) {
            doc += `## Arguments\n`;
            for (const arg of tag.arguments) {
                doc += `- **${arg.name}** (${arg.types.join(' | ')}): ${arg.description}\n`;
            }
            doc += '\n';
        }
        if (tag.hashs && tag.hashs.length > 0) {
            doc += `## Hash Parameters\n`;
            for (const hash of tag.hashs) {
                doc += `- **${hash.name}** (${hash.types.join(' | ')}): ${hash.description}\n`;
            }
            doc += '\n';
        }
        if (tag.examples && tag.examples.length > 0) {
            doc += `## Example\n\`\`\`sline\n${tag.examples[0].raw_sline}\n\`\`\`\n\n`;
        }
        doc += `[📖 Documentation](${tag.link})`;
        return doc;
    }
    formatFilterDocumentation(filter) {
        let doc = `# ${filter.name}\n\n${filter.summary}\n\n`;
        if (filter.syntax) {
            doc += `## Syntax\n\`\`\`sline\n${filter.syntax}\n\`\`\`\n\n`;
        }
        if (filter.arguments && filter.arguments.length > 0) {
            doc += `## Arguments\n`;
            for (const arg of filter.arguments) {
                doc += `- **${arg.name}** (${arg.types.join(' | ')}): ${arg.description}\n`;
            }
            doc += '\n';
        }
        const returnType = Array.isArray(filter.return_type) ? filter.return_type.join(' | ') : filter.return_type;
        if (returnType) {
            doc += `## Returns\n**${returnType}**\n\n`;
        }
        if (filter.examples && filter.examples.length > 0) {
            doc += `## Example\n\`\`\`sline\n${filter.examples[0].raw_sline}\n\`\`\`\n\n`;
        }
        doc += `[📖 Documentation](${filter.link})`;
        return doc;
    }
    formatObjectDocumentation(obj) {
        let doc = `# ${obj.name}\n\n${obj.summary}\n\n`;
        if (obj.properties && obj.properties.length > 0) {
            doc += `## Properties\n`;
            for (const prop of obj.properties.slice(0, 10)) {
                doc += `- **${prop.name}** (${prop.return_type}): ${prop.summary}\n`;
            }
            if (obj.properties.length > 10) {
                doc += `- ... and ${obj.properties.length - 10} more properties\n`;
            }
            doc += '\n';
        }
        doc += `[📖 Documentation](${obj.link})`;
        return doc;
    }
    getTagCompletions(prefix) {
        if (!prefix) {
            return this.tagCompletionItems;
        }
        return this.tagCompletionItems.filter(item => item.label.toLowerCase().startsWith(prefix.toLowerCase()));
    }
    getFilterCompletions(prefix) {
        if (!prefix) {
            return this.filterCompletionItems;
        }
        return this.filterCompletionItems.filter(item => item.label.toLowerCase().startsWith(prefix.toLowerCase()));
    }
    getObjectPropertyCompletions(objectName, prefix) {
        const properties = this.objectProperties.get(objectName);
        if (!properties) {
            return [];
        }
        return properties
            .filter(prop => prop.name.toLowerCase().startsWith(prefix.toLowerCase()))
            .map(prop => this.createPropertyCompletionItem(prop));
    }
    createPropertyCompletionItem(property) {
        return {
            label: property.name,
            kind: node_1.CompletionItemKind.Property,
            detail: `${property.return_type} - ${property.summary}`,
            documentation: {
                kind: node_1.MarkupKind.Markdown,
                value: `**${property.name}** (${property.return_type})\n\n${property.summary}\n\n${property.description || ''}`
            },
            insertText: property.name,
            data: { type: 'property', name: property.name },
            deprecated: property.deprecated,
            sortText: property.deprecated ? 'z' + property.name : 'a' + property.name
        };
    }
    getTagParameterCompletions(tagName, prefix) {
        const tag = this.tags.get(tagName);
        if (!tag) {
            return [];
        }
        const completions = [];
        if (tag.arguments) {
            for (const arg of tag.arguments) {
                if (arg.name.toLowerCase().startsWith(prefix.toLowerCase())) {
                    completions.push({
                        label: arg.name,
                        kind: node_1.CompletionItemKind.Variable,
                        detail: `Parameter - ${arg.types.join(' | ')}`,
                        documentation: arg.description,
                        insertText: arg.name,
                        data: { type: 'parameter', name: arg.name }
                    });
                }
            }
        }
        if (tag.hashs) {
            for (const hash of tag.hashs) {
                if (hash.name.toLowerCase().startsWith(prefix.toLowerCase())) {
                    completions.push({
                        label: hash.name,
                        kind: node_1.CompletionItemKind.Variable,
                        detail: `Hash Parameter - ${hash.types.join(' | ')}`,
                        documentation: hash.description,
                        insertText: `${hash.name}=`,
                        data: { type: 'hash_parameter', name: hash.name }
                    });
                }
            }
        }
        return completions;
    }
    getFilterParameterCompletions(filterName, prefix) {
        const filter = this.filters.get(filterName);
        if (!filter || !filter.arguments) {
            return [];
        }
        return filter.arguments
            .filter(arg => arg.name.toLowerCase().startsWith(prefix.toLowerCase()))
            .map(arg => ({
            label: arg.name,
            kind: node_1.CompletionItemKind.Variable,
            detail: `Parameter - ${arg.types.join(' | ')}`,
            documentation: arg.description,
            insertText: arg.name,
            data: { type: 'filter_parameter', name: arg.name }
        }));
    }
    getHoverInfo(word, context) {
        const tag = this.tags.get(word);
        if (tag) {
            return {
                name: tag.name,
                summary: tag.summary,
                summary_cn: tag.summary_cn,
                syntax: tag.syntax,
                parameters: tag.arguments?.map(arg => ({
                    name: arg.name,
                    types: arg.types,
                    description: arg.description
                })),
                examples: tag.examples,
                link: tag.link,
                deprecated: tag.deprecated
            };
        }
        const filter = this.filters.get(word);
        if (filter) {
            return {
                name: filter.name,
                summary: filter.summary,
                summary_cn: filter.summary_cn,
                syntax: filter.syntax,
                parameters: filter.arguments?.map(arg => ({
                    name: arg.name,
                    types: arg.types,
                    description: arg.description
                })),
                examples: filter.examples,
                link: filter.link,
                deprecated: filter.deprecated,
                returnType: Array.isArray(filter.return_type) ? filter.return_type.join(' | ') : filter.return_type
            };
        }
        const obj = this.objects.get(word);
        if (obj) {
            return {
                name: obj.name,
                summary: obj.summary,
                summary_cn: obj.summary_cn,
                link: obj.link,
                deprecated: obj.deprecated
            };
        }
        if (context?.objectName) {
            const properties = this.objectProperties.get(context.objectName);
            const property = properties?.find(p => p.name === word);
            if (property) {
                return {
                    name: property.name,
                    summary: property.summary,
                    summary_cn: property.summary_cn,
                    link: '',
                    deprecated: property.deprecated,
                    returnType: property.return_type
                };
            }
        }
        return null;
    }
    hasTag(tagName) {
        return this.tags.has(tagName);
    }
    hasFilter(filterName) {
        return this.filters.has(filterName);
    }
    hasObject(objectName) {
        return this.objects.has(objectName);
    }
    getSimilarTags(tagName) {
        const suggestions = [];
        const lowerTagName = tagName.toLowerCase();
        for (const [name] of this.tags) {
            const lowerName = name.toLowerCase();
            if (lowerName.includes(lowerTagName) || lowerTagName.includes(lowerName)) {
                suggestions.push(name);
            }
        }
        return suggestions.slice(0, 3);
    }
    getSimilarFilters(filterName) {
        const suggestions = [];
        const lowerFilterName = filterName.toLowerCase();
        for (const [name] of this.filters) {
            const lowerName = name.toLowerCase();
            if (lowerName.includes(lowerFilterName) || lowerFilterName.includes(lowerName)) {
                suggestions.push(name);
            }
        }
        return suggestions.slice(0, 3);
    }
    getAllTagNames() {
        return Array.from(this.tags.keys());
    }
    getAllFilterNames() {
        return Array.from(this.filters.keys());
    }
    getAllObjectNames() {
        return Array.from(this.objects.keys());
    }
    supplementMissingTags() {
        const allTags = [
            'activate_customer_password_form', 'bind_customer_email_form', 'bind_customer_phone_form',
            'block', 'blocks', 'cancel_delete_customer_form', 'capture', 'cart_form', 'case',
            'company_account_application_form', 'component', 'contact_form', 'content', 'create_customer_form',
            'customer_address_form', 'customer_form', 'customer_login_form', 'customer_login_link',
            'customer_logout_link', 'customer_register_link', 'customer_subscribe_form', 'customer_unsubscribe_form',
            'delete_customer_form', 'external_video_tag', 'for', 'format_address', 'highlight', 'if',
            'image_tag', 'layout', 'link_to', 'link_to_customer_login', 'link_to_customer_logout',
            'link_to_customer_register', 'localization_form', 'metafield_tag', 'new_comment_form',
            'order_tracking_form', 'payment_button', 'payment_type_svg', 'placeholder_svg', 'preload_tag',
            'reset_customer_password_form', 'schema', 'script', 'section', 'sections', 'set',
            'storefront_password_form', 'style', 'stylesheet', 'switch', 'time_tag', 'update_customer_form',
            'var', 'video_tag',
            'comment', 'customer_register_form', 'default', 'each', 'else', 'elseif', 'form', 'liquid',
            'paginate', 'raw', 'recover_customer_password_form', 'tablerow', 'unless', 'with'
        ];
        for (const tagName of allTags) {
            if (!this.tags.has(tagName)) {
                const tag = {
                    name: tagName,
                    syntax: `{{#${tagName}}}...{{/${tagName}}}`,
                    summary: `SLine ${tagName} tag`,
                    summary_cn: `SLine ${tagName} 标签`,
                    examples: [],
                    link: '',
                    deprecated: false
                };
                this.tags.set(tagName, tag);
            }
        }
    }
    supplementMissingFilters() {
        const allFilters = [
            'abs', 'append', 'asset_url', 'at_least', 'at_most', 'camelize', 'capitalize', 'ceil',
            'class_list', 'concat', 'contains', 'css_var', 'date', 'default', 'divided_by', 'downcase',
            'ends_with', 'escape', 'external_video_url', 'file_img_url', 'file_url', 'first', 'floor',
            'font_face', 'font_modify', 'font_url', 'get', 'get_article_pagination', 'get_collections',
            'get_comment_pagination', 'get_metafields', 'get_order_pagination', 'get_pagination',
            'get_product', 'get_product_pagination', 'get_search_pagination', 'get_variants', 'handleize',
            'image_url', 'join', 'json', 'last', 'map', 'metafield_text', 'minus', 'modulo', 'money',
            'money_with_currency', 'money_without_currency', 'newline_to_br', 'payment_type_img_url',
            'pluralize', 'plus', 'prepend', 'remove', 'remove_first', 'replace', 'replace_first',
            'reverse', 'round', 'size', 'slice', 'sort', 'split', 'starts_with', 'strip_html',
            'strip_newlines', 't', 'times', 'trim', 'trim_left', 'trim_right', 'truncate',
            'truncate_words', 'uniq', 'upcase', 'url_decode', 'url_encode', 'url_escape',
            'url_param_escape', 'where'
        ];
        for (const filterName of allFilters) {
            if (!this.filters.has(filterName)) {
                const filter = {
                    name: filterName,
                    syntax: `{{ value | ${filterName} }}`,
                    summary: `SLine ${filterName} filter`,
                    summary_cn: `SLine ${filterName} 过滤器`,
                    arguments: [],
                    examples: [],
                    link: '',
                    deprecated: false,
                    return_type: 'string'
                };
                this.filters.set(filterName, filter);
            }
        }
    }
    loadFallbackData() {
        console.log('Loading fallback SLine reference data...');
        const basicTags = [
            'if', 'else', 'each', 'for', 'with', 'unless',
            'component', 'layout', 'content', 'section', 'sections',
            'blocks', 'block', 'var', 'set', 'capture', 'schema', 'image_tag'
        ];
        const basicFilters = [
            'money', 'money_with_currency', 'money_without_currency',
            'date', 'format', 'upper', 'lower', 'capitalize', 'truncate',
            'trim', 'replace', 'split', 'first', 'last', 'size', 'join',
            'asset_url', 'append', 'prepend', 'strip_html', 'escape',
            'default', 't', 'json', 'class_list', 'get_variants', 'contains',
            'plus', 'minus', 'times', 'divided_by', 'modulo', 'abs', 'ceil', 'floor', 'round'
        ];
        const basicObjects = [
            'product', 'products', 'collection', 'collections', 'variant',
            'cart', 'order', 'customer', 'shop', 'request', 'settings',
            'routes', 'localization', 'article', 'articles', 'blog', 'blogs',
            'page', 'pages', 'block', 'section', 'props', 'forblock', 'this', 'forloop'
        ];
        for (const tagName of basicTags) {
            const tag = {
                name: tagName,
                syntax: `{{#${tagName}}}...{{/${tagName}}}`,
                summary: `SLine ${tagName} tag`,
                summary_cn: `SLine ${tagName} 标签`,
                examples: [],
                link: '',
                deprecated: false
            };
            this.tags.set(tagName, tag);
        }
        for (const filterName of basicFilters) {
            const filter = {
                name: filterName,
                syntax: `{{ value | ${filterName} }}`,
                summary: `SLine ${filterName} filter`,
                summary_cn: `SLine ${filterName} 过滤器`,
                arguments: [],
                examples: [],
                link: '',
                deprecated: false,
                return_type: 'string'
            };
            this.filters.set(filterName, filter);
        }
        for (const objectName of basicObjects) {
            const obj = {
                name: objectName,
                summary: `SLine ${objectName} object`,
                summary_cn: `SLine ${objectName} 对象`,
                link: '',
                deprecated: false,
                properties: []
            };
            this.objects.set(objectName, obj);
        }
        this.precomputeCompletionItems();
        console.log(`Loaded fallback data: ${this.tags.size} tags, ${this.objects.size} objects, ${this.filters.size} filters`);
    }
}
exports.ReferenceDataManager = ReferenceDataManager;
