{"comments": {"blockComment": ["{{!", "--}}"]}, "brackets": [["{", "}"], ["[", "]"], ["(", ")"], ["{{", "}}"], ["{{!", "--}}"], ["{{{", "}}}"]], "autoClosingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["\"", "\""], ["'", "'"], ["`", "`"], ["{{", "}}"], ["{{!", "--}}"], ["{{{", "}}}"]], "surroundingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["\"", "\""], ["'", "'"], ["`", "`"], ["{{", "}}"], ["{{{", "}}}"]], "folding": {"markers": {"start": "^\\s*{{#(if|each|unless|with|component|layout|section|var)\\b", "end": "^\\s*{{/(if|each|unless|with|component|layout|section|var)\\b"}}, "wordPattern": "\\b[a-zA-Z_$][a-zA-Z0-9_$]*\\b", "indentationRules": {"increaseIndentPattern": "^\\s*{{#(if|each|unless|with|component|layout|section|var|schema)\\b", "decreaseIndentPattern": "^\\s*{{/(if|each|unless|with|component|layout|section|var|schema)\\b"}, "onEnterRules": [{"beforeText": "^\\s*{{#(if|each|unless|with|component|layout|section|var|schema)\\b.*$", "action": {"indent": "indent"}}, {"beforeText": "^\\s*{{#else\\b.*$", "action": {"indent": "outdent"}}]}